trigger: none

schedules:
  - cron: "0 0 * * *"
    always: true
    branches:
      include:
        - main

pool:
  vmImage: ubuntu-latest

resources:
  repositories:
    - repository: sbod-core
      type: git
      name: "sbod/sbod-core"
    - repository: sbod-ext
      type: git
      name: "sbod/sbod-ext"
    - repository: sbod-common
      type: git
      name: "sbod/sbod-common"

steps:
  - checkout: self

  - checkout: sbod-core

  - checkout: sbod-ext

  - checkout: sbod-common

  - task: AzureKeyVault@2
    displayName: "Azure KeyVault: Get SecHub API token"
    inputs:
      azureSubscription: "ECE-NPRD-NPRWEUB-SBOD"
      KeyVaultName: "sbod-keepass"
      SecretsFilter: "azure-tenant-id, azure-client-id, azure-client-secret, PIDC1A9-sechub-api-token, technical-user-certificate, technical-user-certificate-passphrase, devopsguard-username, devopsguard-password, devopsguard-apikey"

  - script: |
      sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d -b /usr/local/bin
      cd $(Build.SourcesDirectory)/sbod-core/taskfile
      task container:setupAll
      task container:restartContainer
      task db:createDatabase
      cd $(Build.SourcesDirectory)/sbod-ext/taskfile
      task db:createDatabase

  - task: Gradle@3
    displayName: "Build SBOD core openAPI specifications "
    inputs:
      workingDirectory: "$(Build.SourcesDirectory)/sbod-core"
      gradleWrapperFile: "$(Build.SourcesDirectory)/sbod-core/gradlew"
      gradleOptions: "-Xmx3072m"
      options: "--stacktrace"
      javaHomeOption: "JDKVersion"
      jdkVersionOption: "1.17"
      jdkArchitectureOption: "x64"
      tasks: "generateOpenApiDocs"
    env:
      AZURE_TENANT_ID: $(azure-tenant-id)
      AZURE_CLIENT_SECRET: $(azure-client-secret)
      AZURE_CLIENT_ID: $(azure-client-id)

  - script: |
      cd $(Build.SourcesDirectory)/sbod-ext/taskfile
      task db:createDigitalKeyView

  - task: Gradle@3
    displayName: "Build SBOD ext openAPI specifications "
    inputs:
      workingDirectory: "$(Build.SourcesDirectory)/sbod-ext"
      gradleWrapperFile: "$(Build.SourcesDirectory)/sbod-ext/gradlew"
      gradleOptions: "-Xmx3072m"
      options: "--stacktrace"
      javaHomeOption: "JDKVersion"
      jdkVersionOption: "1.17"
      jdkArchitectureOption: "x64"
      tasks: "generateOpenApiDocs"
    env:
      AZURE_TENANT_ID: $(azure-tenant-id)
      AZURE_CLIENT_SECRET: $(azure-client-secret)
      AZURE_CLIENT_ID: $(azure-client-id)

  - task: Bash@3
    displayName: Install Sechub scanner
    inputs:
      targetType: inline
      script: |
        cd /tmp
        CLIENT_VERSION=$(curl -s https://mercedes-benz.github.io/sechub/latest/client-download.html | grep https://github.com/mercedes-benz/sechub/ | awk -F '-' '{print $NF}' | sed 's/.zip">//')
        curl -L -o sechub-cli.zip https://github.com/mercedes-benz/sechub/releases/download/v$CLIENT_VERSION-client/sechub-cli-$CLIENT_VERSION.zip
        unzip sechub-cli.zip
        sudo cp platform/linux-amd64/sechub /usr/local/bin

  - script: |
      cd $(Build.SourcesDirectory)/sbod-infrastructure/configs/sechub
      tempfile_cert=$(mktemp).p12
      echo "$(technical-user-certificate)" | base64 -d > "$tempfile_cert"
      jq '.data.sources[0].fileSystem.files[0]="$(Build.SourcesDirectory)/sbod-core/build/docs/sbod-api.yaml" | .data.sources[1].fileSystem.files[0]="'"$tempfile_cert"'" | .webScan.clientCertificate.password="$(technical-user-certificate-passphrase)"' "sechub-dast-core.json">"sechub-dast-core-new.json"
      jq '.data.sources[0].fileSystem.files[0]="$(Build.SourcesDirectory)/sbod-ext/build/docs/sbod-api.yaml" | .data.sources[1].fileSystem.files[0]="'"$tempfile_cert"'" | .webScan.clientCertificate.password="$(technical-user-certificate-passphrase)"' "sechub-dast-ext.json">"sechub-dast-ext-new.json"
    displayName: Update sechub configuration with OpenAPI spec and client certificate

  - script: |
      cd $(Build.SourcesDirectory)
      mkdir -p reports

      FILEPATH="$(Build.SourcesDirectory)/reports/sechub-$(echo $(Build.BuildNumber) | sed 's/\./_/')"
      CONFIGFILE="sbod-infrastructure/configs/sechub/sechub.json"
      echo "##vso[task.setvariable variable=reportPath;]$FILEPATH";

      sechub scan  -configfile $CONFIGFILE  -reportformat html  -output $FILEPATH.html
      sechub getReport  -project sbod  -reportformat json  -output $FILEPATH.json

      COREDASTCONFIGFILE="$(Build.SourcesDirectory)/sbod-infrastructure/configs/sechub/sechub-dast-core-new.json"
      DASTCOREFILEPATH="$(Build.SourcesDirectory)/reports/sechub-dast-core-$(echo $(Build.BuildNumber))"
      sechub scan  -configfile $COREDASTCONFIGFILE  -reportformat html  -output $DASTCOREFILEPATH.html
      sechub getReport  -project sbod  -reportformat json  -output $DASTCOREFILEPATH.json
      echo "##vso[task.setvariable variable=dastCoreFilepath;]$DASTCOREFILEPATH";

      EXTDASTCONFIGFILE="$(Build.SourcesDirectory)/sbod-infrastructure/configs/sechub/sechub-dast-ext-new.json"
      DASTEXTFILEPATH="$(Build.SourcesDirectory)/reports/sechub-dast-ext-$(echo $(Build.BuildNumber))"
      sechub scan  -configfile $EXTDASTCONFIGFILE  -reportformat html  -output $DASTEXTFILEPATH.html
      sechub getReport  -project sbod  -reportformat json  -output $DASTEXTFILEPATH.json
      echo "##vso[task.setvariable variable=dastExtFilepath;]$DASTEXTFILEPATH";

    displayName: "SecHub Scan"
    env:
      SECHUB_SERVER: https://sechub.corpinter.net
      SECHUB_USERID: pidc1a9
      SECHUB_APITOKEN: $(PIDC1A9-sechub-api-token)

  - publish: $(Build.SourcesDirectory)/reports
    artifact: "SecHub Report"

  - template: "../templates/devopsguard-upload.yml"
    parameters:
      productName: "sechub_sbod"
      engagementName: "sechub_sbod"
      apiKey: $(devopsguard-apikey)
      authentication: "$(devopsguard-username):$(devopsguard-password)"
      testType: "codescan,secretscan"
      reportPath: $(reportPath).json

  - template: "../templates/devopsguard-upload.yml"
    parameters:
      productName: "sechub_sbod"
      engagementName: "sechub_sbod"
      apiKey: $(devopsguard-apikey)
      authentication: "$(devopsguard-username):$(devopsguard-password)"
      testType: webscan
      reportPath: $(dastCoreFilepath).json

  - template: "../templates/devopsguard-upload.yml"
    parameters:
      productName: "sechub_sbod"
      engagementName: "sechub_sbod"
      apiKey: $(devopsguard-apikey)
      authentication: "$(devopsguard-username):$(devopsguard-password)"
      testType: webscan
      reportPath: $(dastExtFilepath).json

# ------------------------------------------------------------------------------------------------------------------ #
#   Variables                                                                                                        #
# ------------------------------------------------------------------------------------------------------------------ #
variables:
  # ------------------------------- #
  #     Environment related vars    #
  # ------------------------------- #

  # My environment folder. Must match folder name in configs folder, CaseSensitive!!!
  MY_ENV: "sbod_postgresflex"

  # Description of the environment. Will be used to generate container names, only lowercase letters allowed!!!
  # Must be changed/different for every environment!!! Examples: eceprod/amtm/amgw/cntm...
  # Used to auto-generate storage container names.
  ENV_DESC: "sbod"

  # My region. Valid options are "china" (for Azure CN cloud deployments) or "other" (EMEA, AMAP deployments), CaseSensitive!!!
  # Default option is "other"
  REG_NAME: "other"

  # Need to be the name of cluster's var folders under MY_ENV Folder, CaseSensitive!!!
  # Examples: ece_prod_1/ece_prod_2
  DB_VAR_FOLDER: "nonprod"

  # ------------------------------- #
  #     Project related vars        #
  # ------------------------------- #

  # Folders directory between root repo and MY_ENV Folder.
  # If env folder is directly in repo root, LEAVE IT BLANK, like this ''
  # If directory has value, set the directory WITHOUT / at the beginning but MUST have / at the end!!!
  # Example: MY_ENV is ECETEST then Prefix should be 'infrastructure-values/ssoalpha/terraform/'
  VARS_PATH_DIR: "configs/"

  # azureSubscription - this referring to ADO service connection
  SERVICE_CONNECTION: "ECE-NPRD-NPRWEUB-SBOD"
  AZ_SUBSCRIPTION_ID: "e30c9817-212f-421b-bbdb-d831961e90c8"
  AZ_TENANT_ID: "c98d2fc9-def0-43ca-a603-a8a1e287fa9d"

  # Storage Account default values (name can contain only lowercase letters and numbers).
  # Every environment can have different account, but can also share the same account.
  # Appropriate storage containers will be made for every stage.
  ST_ACCOUNT_RG_LOCATION: "westeurope"
  ST_ACCOUNT_RG_NAME: "sbodtfstate"
  ST_ACCOUNT_NAME: "sbodtfstatenonprod"

  # Vault name. Contains clientID (and Secret) of Service Principal(sp-ece-nprweub-sbod) for aks provisioning
  VAULT_NAME: "sbod-nonprod"
  VAULT_SECRET_1: "sp-ece-nprweub-sbod-clientid"
  VAULT_SECRET_2: "sp-ece-nprweub-sbod-clientsecret"
# ------------------------------------------------------------------------------------------------------------------ #

# ------------------------------------------------------------------------------------------------------------------ #
#   Deployment stages                                                                                                #
# ------------------------------------------------------------------------------------------------------------------ #
parameters:
  - name: Preview
    displayName: Preview (Skip Terraform Apply)
    type: boolean
    default: true

trigger: none

stages:
  - stage: deploy_postgresql_nonprod_weu
    displayName: Deploy PostgreSQL Flexible Server NONPROD WEU
    jobs:
      - job: "deploy_postgresql_nonprod_weu"
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - template: ../templates/postgresflex.yaml
            parameters:
              envName: ${{ variables.MY_ENV }}
              regName: ${{ variables.REG_NAME }}
              VarFolder: ${{ variables.DB_VAR_FOLDER }}
              VaultName: ${{ variables.VAULT_NAME }}
              VaultSecret1: ${{ variables.VAULT_SECRET_1 }}
              VaultSecret2: ${{ variables.VAULT_SECRET_2 }}
              ServiceConnection: ${{ variables.SERVICE_CONNECTION }}
              AzureSubscriptionId: ${{ variables.AZ_SUBSCRIPTION_ID }}
              AzureTenantId: ${{ variables.AZ_TENANT_ID }}
              ResourceGroupName: ${{ variables.ST_ACCOUNT_RG_NAME }}
              ResourceGroupLocation: ${{ variables.ST_ACCOUNT_RG_LOCATION }}
              StorageAccountName: ${{ variables.ST_ACCOUNT_NAME }}
              StorageContainerName: tstate-${{ variables.ENV_DESC }}-db
              TfstateBlobName: cluster_state_${{ variables.DB_VAR_FOLDER }}
              varDir: ${{ variables.VARS_PATH_DIR }}
              Preview: ${{ parameters.Preview }}
# ------------------------------------------------------------------------------------------------------------------ #

parameters:
  - name: schema_name
    displayName: Schema name (Will also be used as role name)
    type: string
  - name: environment
    displayName: Environment to deploy
    type: string
    values:
      - dev
      - nonprod
      - prod
      - feature
    default: dev
  - name: workloadIdentityOid
    displayName: Workload Identity Object ID for schema's role
    type: string

trigger: none

variables:
  SCHEMA_NAME: ${{ parameters.schema_name }}_${{ parameters.environment }}

  ${{ if eq(parameters.environment, 'feature') }}:
    VAULT_SECRET_KEY: jdbc-${{ parameters.schema_name }}-feature-user
    APPLICATION_VAULT_NAME: "sbod-dev"
  ${{ else }}:
    VAULT_SECRET_KEY: jdbc-${{ parameters.schema_name }}-user
    APPLICATION_VAULT_NAME: sbod-${{ parameters.environment }} # Vault name to store secrets for Java use (e.g.: sbod-dev, sbod-nonprod)

  ${{ if in(parameters.environment, 'dev', 'feature') }}:
    SERVICE_CONNECTION: "azurerm-federated-dev"
    SP_VAULT_NAME: "sbod-kv" # Vault name to get PostgreSQL JDBC credentials
  ${{ if eq(parameters.environment, 'nonprod') }}:
    SERVICE_CONNECTION: "azurerm-federated-nonprod"
    SP_VAULT_NAME: "sbod-keepass"
  ${{ if eq(parameters.environment, 'prod') }}:
    SERVICE_CONNECTION: "azurerm-federated-prod" # TODO: Create this service connection in the future for prod
    SP_VAULT_NAME: "sbod-keepass-prod" # TODO: Create this vault in the future for prod

pool:
  vmImage: "ubuntu-latest"

steps:
  - template: ../templates/create-db-role-template.yaml
    parameters:
      serviceConnection: ${{ variables.SERVICE_CONNECTION }}
      spVaultName: ${{ variables.SP_VAULT_NAME }}
      applicationVaultName: ${{ variables.APPLICATION_VAULT_NAME }}
      vaultSecretKey: ${{ variables.VAULT_SECRET_KEY }}
      schemaName: ${{ variables.SCHEMA_NAME }}
      workloadIdentityOid: ${{ parameters.workloadIdentityOid }}

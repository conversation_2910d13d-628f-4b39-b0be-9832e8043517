variables:
  allStages: true

trigger: none

stages:
  ### WEU
  - stage: prom_ece_sbod_ece_1_v1_weu
    displayName: Deploy SBOD PROMETHEUS DEV on WEU
    jobs:
      - template: "../templates/prometheus/prometheus-deploy-job.yaml"
        parameters:
          jobName: job_deploy_prom_ece_dev_1_v1_weu
          subscription: "ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD"
          subscriptionVault: "ECE-DEV-DEVWEUB-SBOD"
          aksClusterResourceGroup: "SBOD-DEV-K8S-V1-westeurope"
          aksClusterName: "sbod_ece_1_v1_weu"
          monPromNamespace: "monitoring"
          monPromHelmReleaseName: "prometheus"
          monPromChartName: "prometheus-community/kube-prometheus-stack"
          monPromChartVersion: "48.2.3"
          monPromChartValueFile: "$(System.DefaultWorkingDirectory)/helm-values/kube-prometheus-stack/other/config.yaml"
          monBlackNamespace: "monitoring"
          monBlackHelmReleaseName: "blackbox"
          monBlackChartName: "prometheus-community/prometheus-blackbox-exporter"
          monBlackChartVersion: "8.3.0"
          monBlackChartValueFile: "$(System.DefaultWorkingDirectory)/helm-values/prometheus-blackbox-exporter/other/config.yaml"
          deployProceed: true

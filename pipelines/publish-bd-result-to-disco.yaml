trigger: none

schedules:
  - cron: "0 2 * * *"
    always: true
    branches:
      include:
        - main

pool:
  vmImage: ubuntu-latest

variables:
  AZURE_SUBSCRIPTION: ECE-NPRD-NPRWEUB-SBOD
  VAULT_NAME: sbod-keepass
  BD_HOST: https://bdscan-rd.i.mercedes-benz.com


stages:
  - stage: upload_sbod_core_bd_result_to_disco
    displayName: Upload SBOD core BD scan to Disco
    jobs:
      - job: "upload_sbod_core_bd_result_to_disco"
        steps:
          - task: AzureKeyVault@2
            displayName: "Get access token"
            inputs:
              azureSubscription: "${{ variables.AZURE_SUBSCRIPTION }}"
              KeyVaultName: "${{ variables.VAULT_NAME }}"
              SecretsFilter: "disco-sbod-core-token, PIDC1A9-blackduck-access-token"

          - template: ../templates/publish-bd-result-to-disco.yaml
            parameters:
              BLACK_DUCK_API_TOKEN: $(PIDC1A9-blackduck-access-token)
              BLACK_DUCK_URL: "${{ variables.BD_HOST }}"
              BLACK_DUCK_PROJECT_NAME: "SBOD-SBODCORE"
              BLACK_DUCK_PROJECT_VERSION: "latest"
              DISCO_API_TOKEN: $(disco-sbod-core-token)
              DISCO_HOST: "https://production.gorillas.mercedes-benz.com/disco/v1"
              DISCO_PROJECT_UUID: "fe7e0098-c02b-49a9-8548-eb9368a0c33c"
              DISCO_PROJECT_VERSION: "main"
              DISCO_SBOM_TAG: "$(Build.BuildNumber)"
              BLACK_DUCK_MB_TEMPLATE: ""

  - stage: upload_sbod_ext_bd_result_to_disco
    displayName: Upload SBOD ext BD scan to Disco
    jobs:
      - job: "upload_sbod_core_bd_result_to_disco"
        steps:
          - task: AzureKeyVault@2
            displayName: "Get access token"
            inputs:
              azureSubscription: "${{ variables.AZURE_SUBSCRIPTION }}"
              KeyVaultName: "${{ variables.VAULT_NAME }}"
              SecretsFilter: "disco-sbod-ext-token, PIDC1A9-blackduck-access-token"


          - template: ../templates/publish-bd-result-to-disco.yaml
            parameters:
              BLACK_DUCK_API_TOKEN: $(PIDC1A9-blackduck-access-token)
              BLACK_DUCK_URL: "${{ variables.BD_HOST }}"
              BLACK_DUCK_PROJECT_NAME: "SBOD-SBODEXT"
              BLACK_DUCK_PROJECT_VERSION: "latest"
              DISCO_API_TOKEN: $(disco-sbod-ext-token)
              DISCO_HOST: "https://production.gorillas.mercedes-benz.com/disco/v1"
              DISCO_PROJECT_UUID: "2b15312d-6d56-4d55-9c60-d04a21df07f0"
              DISCO_PROJECT_VERSION: "main"
              DISCO_SBOM_TAG: "$(Build.BuildNumber)"
              BLACK_DUCK_MB_TEMPLATE: ""
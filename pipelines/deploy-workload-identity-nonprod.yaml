parameters:
  - name: servicePrincipleClientId
    displayName: Service Principle Id (Required)
    default: "56d56691-xxxx-xxxx-xxxx-7978412365ec"
  - name: servicePrincipleObjectId
    displayName: Service Principle Object Id (Required) - Find in App Registration
    default: "4e92e0d8-xxxx-xxxx-xxxx-f52c90b6685f"
  - name: serviceAccountName
    displayName: Service Account Name
    default: "sbod-core-nonprod-workload-identity"

variables:
  allStages: true

trigger: none

stages:
  ### WEU
  - stage: workload_identity_dev_ece_1_v1_weu
    displayName: Deploy Workload Identity DEV on WEU
    jobs:
      - template: "../templates/workload-identity/workload-identity-deploy-job.yaml"
        parameters:
          jobName: job_deploy_workload_identity_dev_ece_1_v1_weu
          akvName: "sbod-nonprod"
          subscription: "ECE-NPRD-NPRWEUB-SBOD"
          subscriptionId: "MIC-NPRD-ECE-WESTEUROPE-SBOD"
          aksClusterResourceGroup: "SBOD-NONPROD-K8S-V1-westeurope"
          aksClusterName: "sbod_ece_1_v1_weu"
          workloadNamespace: "azure-workload-identity-system"
          servicePrincipleClientId: "${{ parameters.servicePrincipleClientId }}"
          servicePrincipleObjectId: "${{ parameters.servicePrincipleObjectId }}"
          serviceAccountNS: "sbod-nonprod"
          serviceAccountName: "${{ parameters.serviceAccountName }}"

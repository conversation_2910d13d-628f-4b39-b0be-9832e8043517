parameters:
  - name: branchToDeploy
    displayName: Branch name to deploy
    default: "Example: features/xxx"
  - name: skipTestCase
    displayName: Skip Test case
    type: boolean
    default: true

variables:
  ${{ if eq(variables['Build.Reason'], 'Manual') }}:
    COMMON_RELEASE_NAME: sbod-common-${{ replace(parameters.branchToDeploy, 'features/', '' )}}
    IMAGE_TAG: ${{ replace(parameters.branchToDeploy, 'features/', '' )}}
    CHART_RELEASE_NAME: sbod-core-feature-${{ replace(parameters.branchToDeploy, 'features/', '' )}}
    BRANCH_NAME: ${{ parameters.branchToDeploy }}
  ${{ else }}:
    COMMON_RELEASE_NAME: sbod-common-${{ replace(variables['Build.SourceBranch'], 'refs/heads/features/', '') }}
    IMAGE_TAG: ${{ replace(variables['Build.SourceBranch'], 'refs/heads/features/', '') }}
    CHART_RELEASE_NAME: sbod-core-feature-${{ replace(variables['Build.SourceBranch'], 'refs/heads/features/', '') }}
    BRANCH_NAME: ${{ replace(variables['Build.SourceBranch'], 'refs/heads/', '') }}

  AZURE_SUBSCRIPTION: ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD
  AZURE_K8S_RESOURCE_GROUP: SBOD-DEV-K8S-V1-westeurope
  KUBERNETES_CLUSTER: sbod_ece_1_v1_weu
  CHART_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/sbod-core
  CONFIG_FILE_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/sbod-core/feature-dev.yaml
  VAULT_NAME: "sbod-dev"
  REPO_NAME: "sbod-core"
  NAMESPACE: "sbod-dev"
  HELM_VERSION: v3.12.3
  ACR_NAME: acr-sc-dev

trigger:
  - none

resources:
  repositories:
    - repository: sbod-core
      type: git
      name: "sbod/sbod-core" # repository in Azure DevOps
      trigger:
        - features/*
    - repository: sbod-common
      type: git
      name: "sbod/sbod-common" # repository in Azure DevOps
      trigger:
        - none

pool:
  vmImage: ubuntu-latest

stages:
  - stage: deploy_sbod_microservices
    displayName: Deploy SBOD microservice
    jobs:
      - job: "deploy_sbod_microservices"
        steps:
          - template: ../templates/deploy-sbod-microservices.yaml
            parameters:
              commonReleaseName: $(COMMON_RELEASE_NAME)
              imageTag: $(IMAGE_TAG)
              chartReleaseName: $(CHART_RELEASE_NAME)
              branchName: $(BRANCH_NAME)
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION}}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP}}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER}}
              helmVersion: ${{ variables.HELM_VERSION}}
              chartPath: ${{ variables.CHART_PATH}}
              configFilePath: ${{ variables.CONFIG_FILE_PATH}}
              keyvaultName: ${{ variables.VAULT_NAME }}
              repoName: ${{ variables.REPO_NAME }}
              namespace: ${{ variables.NAMESPACE }}
              skipTestCase: ${{ parameters.skipTestCase }}
              environment: "feature-branch"
              acrName: ${{ variables.ACR_NAME }}

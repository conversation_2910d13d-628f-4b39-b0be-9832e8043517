parameters:
  - name: branchToDeploy
    displayName: Branch name to deploy [Do not change this unless doing hotfix]
    default: "main"
  - name: environment
    displayName: Environment to deploy
    type: string
    values:
      - dev
      - nonprod
      - prod
    default: dev
  - name: buildAndDeploy
    displayName: Build and deploy [If false then build only]
    type: boolean
    default: true
  - name: application
    displayName: Environment to deploy
    type: string
    values:
      - sbod-core
      - sbod-ext
      - both
    default: both
  - name: releaseVersion
    displayName: "Release version [Use semantic versioning, e.g.: 1.0.0]"
    type: string

variables:
  ${{ if eq(parameters.environment, 'dev') }}:
    IMAGE_TAG: ${{ parameters.releaseVersion }}
    BRANCH_NAME: ${{ parameters.branchToDeploy }}
    AZURE_SUBSCRIPTION: ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD
    AZURE_K8S_RESOURCE_GROUP: SBOD-DEV-K8S-v1-westeurope
    VAULT_NAME: "sbod-dev"
    NAMESPACE: "sbod-dev"
    ACR_NAME: acr-sc-dev
  ${{ if eq(parameters.environment, 'nonprod') }}:
    IMAGE_TAG: ${{ parameters.releaseVersion }}
    BRANCH_NAME: ${{ parameters.branchToDeploy }}
    AZURE_SUBSCRIPTION: ECE-NPRD-NPRWEUB-SBOD
    AZURE_K8S_RESOURCE_GROUP: SBOD-NONPROD-K8S-V1-westeurope
    VAULT_NAME: "sbod-nonprod"
    NAMESPACE: "sbod-nonprod"
    ACR_NAME: acr-sc
  ${{ if eq(parameters.environment, 'prod') }}:
    IMAGE_TAG: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]
    BRANCH_NAME: $[replace(variables['Build.SourceBranch'], 'refs/heads/', '')]
    AZURE_SUBSCRIPTION: ECE-NPRD-NPRWEUB-SBOD
    AZURE_K8S_RESOURCE_GROUP: SBOD-NONPROD-K8S-V1-westeurope
    VAULT_NAME: "sbod-dev"
    NAMESPACE: "sbod-dev"
    ACR_NAME: sbodacr

  COMMON_RELEASE_NAME: ${{ parameters.releaseVersion }}
  KUBERNETES_CLUSTER: sbod_ece_1_v1_weu
  HELM_VERSION: v3.12.3

trigger:
  - none

resources:
  repositories:
    - repository: sbod-core
      type: git
      name: "sbod/sbod-core"
    - repository: sbod-ext
      type: git
      name: "sbod/sbod-ext"
    - repository: sbod-common
      type: git
      name: "sbod/sbod-common"

pool:
  vmImage: ubuntu-latest

stages:
  - stage: security_scan
    displayName: Perform Security Scanning
    jobs:
      - job: "bdscan_sbod_core"
        condition: in('${{ parameters.application }}', 'sbod-core', 'both')
        steps:
          - checkout: sbod-core
          - template: ../templates/security/blackduck/blackduck-scan.yaml
            parameters:
              projectName: "SBOD-SBODCORE"
              repo: "$(Build.SourcesDirectory)"
              rapidScan: true
      - job: "bdscan_sbod_ext"
        condition: in('${{ parameters.application }}', 'sbod-ext', 'both')
        steps:
          - checkout: sbod-ext
          - template: ../templates/security/blackduck/blackduck-scan.yaml
            parameters:
              projectName: "SBOD-SBODEXT"
              repo: "$(Build.SourcesDirectory)"
              rapidScan: true

  - stage: deploy_sbod_microservices
    displayName: Deploy SBOD microservice
    jobs:
      - job: deploy_sbod_core
        displayName: Deploy SBOD Core
        condition: and(succeeded(), in('${{ parameters.application }}', 'sbod-core', 'both'))
        steps:
          - template: ../templates/deploy-sbod-microservices.yaml
            parameters:
              commonReleaseName: sbod-common-$(COMMON_RELEASE_NAME)
              imageTag: $(IMAGE_TAG)
              chartReleaseName: "sbod-core"
              branchName: $(BRANCH_NAME)
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION}}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP}}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER}}
              helmVersion: ${{ variables.HELM_VERSION}}
              chartPath: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/sbod-core
              configFilePath: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/sbod-core/${{parameters.environment}}.yaml
              keyvaultName: ${{ variables.VAULT_NAME }}
              repoName: "sbod-core"
              namespace: ${{ variables.NAMESPACE }}
              skipTestCase: true
              environment: ${{parameters.environment}}
              acrName: ${{ variables.ACR_NAME }}
              buildAndDeploy: ${{ parameters.buildAndDeploy }}
      - job: deploy_sbod_ext
        displayName: Deploy SBOD Ext
        condition: and(succeeded(), in('${{ parameters.application }}', 'sbod-ext', 'both'))
        steps:
          - template: ../templates/deploy-sbod-microservices.yaml
            parameters:
              commonReleaseName: $(COMMON_RELEASE_NAME)
              imageTag: $(IMAGE_TAG)
              chartReleaseName: "sbod-ext"
              branchName: $(BRANCH_NAME)
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION}}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP}}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER}}
              helmVersion: ${{ variables.HELM_VERSION}}
              chartPath: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/sbod-ext
              configFilePath: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/sbod-ext/${{parameters.environment}}.yaml
              keyvaultName: ${{ variables.VAULT_NAME }}
              repoName: "sbod-ext"
              namespace: ${{ variables.NAMESPACE }}
              skipTestCase: true
              environment: ${{parameters.environment}}
              acrName: ${{ variables.ACR_NAME }}
              buildAndDeploy: ${{ parameters.buildAndDeploy }}

  - stage: update_sbod_openapi_spec
    displayName: Update SBOD openapi spec
    condition: and(succeeded(), eq('${{ parameters.environment }}', 'nonprod'))
    jobs:
      - job: "update_sbod_openapi_spec"
        steps:
          - template: ../templates/deploy-microservices/update-openapi-spec.yaml

  - stage: publish_dora_metrics_sbod_core
    displayName: "Publish DORA Metrics SBOD Core"
    condition: and(succeeded(), and(in('${{ parameters.application }}', 'sbod-core', 'both'), eq('${{ parameters.environment }}', 'prod')))
    jobs:
      - job: "dora_sbod_core"
        steps:
          - template: ../templates/dora/sbod-dorawebhook.yaml
            parameters:
              repositoryUrl: "https://<EMAIL>/daimler-mic/sbod/_git/sbod-core"
              application: sbod-core

  - stage: publish_dora_metrics_sbod_ext
    displayName: "Publish DORA Metrics SBOD Ext"
    condition: and(succeeded(), and(in('${{ parameters.application }}', 'sbod-ext', 'both'), eq('${{ parameters.environment }}', 'prod')))
    jobs:
      - job: "dora_sbod_ext"
        steps:
          - template: ../templates/dora/sbod-dorawebhook.yaml
            parameters:
              repositoryUrl: "https://<EMAIL>/daimler-mic/sbod/_git/sbod-ext"
              application: sbod-ext

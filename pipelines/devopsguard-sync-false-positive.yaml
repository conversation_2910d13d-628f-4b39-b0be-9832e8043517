trigger: none

schedules:
  - cron: "0 23 * * *"
    always: true
    branches:
      include:
        - main

variables:
  AZURE_SUBSCRIPTION: ECE-NPRD-NPRWEUB-SBOD
  VAULT_NAME: sbod-keepass
  DEVOPSGUARD_PRODUCT_NAME: sechub_sbod
  SECHUB_URL: https://sechub.corpinter.net
  SECHUB_PROJECT_NAME: sbod
  SECHUB_USER: pidc1a9

steps:
  - task: AzureKeyVault@2
    displayName: "Azure KeyVault: Get secrets"
    inputs:
      azureSubscription: "${{ variables.AZURE_SUBSCRIPTION }}"
      KeyVaultName: "${{ variables.VAULT_NAME }}"
      SecretsFilter: "devopsguard-apikey, devopsguard-username, devopsguard-password, PIDC1A9-sechub-api-token"

  - task: AzureCLI@1
    displayName: "Upload SecHub false positive to DevOpsGuard"
    inputs:
      azureSubscription: "${{ variables.AZURE_SUBSCRIPTION }}"
      scriptPath: "$(System.DefaultWorkingDirectory)/scripts/mark_false_positive.sh"
      arguments: "${{ variables.DEVOPSGUARD_PRODUCT_NAME }} $(devopsguard-apikey) $(devopsguard-username):$(devopsguard-password) ${{ variables.SECHUB_URL }} ${{ variables.SECHUB_PROJECT_NAME }} ${{ variables.SECHUB_USER }} $(PIDC1A9-sechub-api-token)"
      workingDirectory: "$(System.DefaultWorkingDirectory)/scripts/"

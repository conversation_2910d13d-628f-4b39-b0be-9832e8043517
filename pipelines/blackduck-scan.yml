trigger: none

schedules:
  - cron: "0 0 * * *"
    always: true
    branches:
      include:
        - main

pool:
  vmImage: ubuntu-latest

resources:
  repositories:
    - repository: sbod-core
      type: git
      name: "sbod/sbod-core"
    - repository: sbod-ext
      type: git
      name: "sbod/sbod-ext"

jobs:
  - job: ScanCore
    steps:
      - checkout: sbod-core
      - template: ../templates/security/blackduck/blackduck-scan.yaml
        parameters:
          projectName: "SBOD-SBODCORE"
          repo: "$(Build.SourcesDirectory)"
          version: "latest"
          rapidScan: false
          failOnSeverities: true

  - job: ScanExt
    steps:
      - checkout: sbod-ext
      - template: ../templates/security/blackduck/blackduck-scan.yaml
        parameters:
          projectName: "SBOD-SBODEXT"
          repo: "$(Build.SourcesDirectory)"
          version: "latest"
          rapidScan: false
          failOnSeverities: true

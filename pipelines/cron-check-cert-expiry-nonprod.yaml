variables:
  # ------------------------------- #
  #     Project related vars        #
  # ------------------------------- #

  # azureSubscription
  # AZ_SUBSCRIPTION: 'MIC-NPRD-ECE-WESTEUROPE-SBOD'
  AZ_SUBSCRIPTION: "ECE-NPRD-NPRWEUB-SBOD"
  AZURE_K8S_RESOURCE_GROUP: SBOD-NONPROD-K8S-V1-westeurope
  KUBERNETES_CLUSTER: sbod_ece_1_v1_weu

  # Kubernetes Secrets
  SECRET_NAMESPACE: mic-tgw-sbod-nonprod
  SECRET_NAME: gw-clntcert-b731330f-f777-4086-aeb9-568ba7f5b76d

  # Vault name.
  VAULT_NAME: "sbod-nonprod"

  # HostName and Request Parameters
  HOST_NAME: "https://sbod-nonprod.query.api.dvb.corpinter.net/core/job/getExpiredCertificates"
  REQUEST_PARAM_KEY: "days"
  REQUEST_PARAM_VALUES: 1,3,5,7
  REQUEST_BODY: ""
  ENDPOINT_NAME: "Check Cert Expiry"
  METHOD: "POST"
# ------------------------------------------------------------------------------------------------------------------ #

schedules:
  - cron: "0 12 * * *"
    displayName: Daily midnight build
    branches:
      include:
        - main
    always: true

trigger: none

stages:
  - stage: cron_job_check_cert_expiry
    displayName: Check the cert expiry and email
    jobs:
      - job: "check_cert_expiry"
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - ${{ each REQUEST_PARAM_VALUE in split(variables.REQUEST_PARAM_VALUES, ',') }}:
              - template: ../templates/cron-jobs/cron-call-request-template.yaml
                parameters:
                  azureSubscription: ${{ variables.AZ_SUBSCRIPTION }}
                  vaultName: ${{ variables.VAULT_NAME }}
                  hostName: ${{ variables.HOST_NAME }}
                  requestParamKey: ${{ variables.REQUEST_PARAM_KEY }}
                  requestParam: ${{ REQUEST_PARAM_VALUE }}
                  requestBody: ""
                  endPointName: ${{ variables.ENDPOINT_NAME }}
                  method: ${{ variables.METHOD }}
                  secretName: ${{ variables.SECRET_NAME }}
                  secretNamespace: ${{ variables.SECRET_NAMESPACE }}
                  azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP }}
                  kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER }}

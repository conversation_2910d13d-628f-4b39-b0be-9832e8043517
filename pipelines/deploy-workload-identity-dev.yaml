parameters:
  - name: servicePrincipleClientId
    displayName: Service Principle Id (Required)
    default: "9e009aac-xxxx-xxxx-xxxx-117dc46ebed9"
  - name: servicePrincipleObjectId
    displayName: Service Principle Object Id (Required)
    default: "c1e1106f-xxxx-xxxx-xxxx-1e9fe02e16b8"
  - name: serviceAccountName
    displayName: Service Account Name
    default: "sbod-core-test-workload-identity"

variables:
  allStages: true

trigger: none

name: "Please refer to App Registration and find the service principles"

stages:
  ### WEU
  - stage: workload_identity_dev_ece_1_v1_weu
    displayName: Deploy Workload Identity DEV on WEU
    jobs:
      - template: "../templates/workload-identity/workload-identity-deploy-job.yaml"
        parameters:
          jobName: job_deploy_workload_identity_dev_ece_1_v1_weu
          akvName: "sbod-dev"
          subscription: "ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD"
          subscriptionId: "MIC-DEV-ECE-WESTEUROPE-SBOD"
          aksClusterResourceGroup: "SBOD-DEV-K8S-V1-westeurope"
          aksClusterName: "sbod_ece_1_v1_weu"
          workloadNamespace: "azure-workload-identity-system"
          servicePrincipleClientId: "${{ parameters.servicePrincipleClientId }}"
          servicePrincipleObjectId: "${{ parameters.servicePrincipleObjectId }}"
          serviceAccountNS: "sbod-dev"
          serviceAccountName: "${{ parameters.serviceAccountName }}"

# trigger:
#   batch: true
#   branches:
#     include:
#       - main
#       - features/*
trigger: none

variables:
  AZURE_SUBSCRIPTION: ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD
  AZURE_K8S_RESOURCE_GROUP: SBOD-DEV-K8S-V1-westeurope
  KUBERNETES_CLUSTER: sbod_ece_1_v1_weu
  NAMESPACE: sbod-dev
  CHART_PATH: $(System.DefaultWorkingDirectory)/helm-charts/service-mesh/sbod-ext
  CONFIG_FILE_PATH: $(System.DefaultWorkingDirectory)/helm-values/service-mesh/sbod-ext/dev.yaml
  CHART_RELEASE_NAME: service-mesh-sbod-ext-dev

stages:
  - stage: deploy_service_mesh_in_sbod_ext_dev
    displayName: Deploy Service Mesh
    jobs:
      - job: "deploy_service_mesh_in_sbod_ext_dev"
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - template: ../templates/service-mesh/deploy-sbod-ext-steps.yaml # Template reference
            parameters:
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION }}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP }}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER }}
              namespace: ${{ variables.NAMESPACE }}
              chartPath: ${{ variables.CHART_PATH }}
              configFilePath: ${{ variables.CONFIG_FILE_PATH }}
              chartReleaseName: ${{ variables.CHART_RELEASE_NAME }}

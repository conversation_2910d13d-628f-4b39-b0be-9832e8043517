trigger: none

variables:
  AZURE_SUBSCRIPTION: ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD
  AZURE_K8S_RESOURCE_GROUP: SBOD-DEV-K8S-V1-westeurope
  KUBERNETES_CLUSTER: sbod_ece_1_v1_weu
  NAMESPACE: argocd
  AKV_NAME: sbod-keepass

  ARGOCD_CHART_PATH: $(System.DefaultWorkingDirectory)/helm-charts/argo-cd
  ARGOCD_CONFIG_FILE_PATH: $(System.DefaultWorkingDirectory)/helm-values/argo-cd/argo-cd.yaml
  ARGOCD_CHART_RELEASE_NAME: argocd

stages:
  - stage: deploy_argocd
    displayName: Deploy ArgoCD
    jobs:
      - job: "deploy_argocd"
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - template: ../templates/argo-cd/deploy-argocd-steps.yaml # Template reference
            parameters:
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION }}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP }}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER }}
              namespace: ${{ variables.NAMESPACE }}
              chartPath: ${{ variables.ARGOCD_CHART_PATH }}
              configFilePath: ${{ variables.ARGOCD_CONFIG_FILE_PATH }}
              chartReleaseName: ${{ variables.ARGOCD_CHART_RELEASE_NAME }}
              akvName: ${{ variables.AKV_NAME }}

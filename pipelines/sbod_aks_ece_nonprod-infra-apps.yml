variables:
  # ------------------------------- #
  #     Environment related vars    #
  # ------------------------------- #

  # Must be provided. Consist of variables: cl_name-location, see cluster var file for values
  # Used to determine the cluster's IP addresses ID for TM endpoint configuration
  CLUSTER_NAME_01: 'sbod_ece_1_v1_weu'

  # Ingress Controller. Config values: test.yaml or production.yaml (depending on the environment)
  INGRESS_CONFIG_PATH: 'helm-values/nginx-ingress/other/config.yaml'
  INGRESS_CHART_VERSION: '4.7.1'

  # Monitoring vars
  MONITORING_CONFIG_PATH: 'helm-values/kube-prometheus-stack/other/config.yaml'
  MONITORING_CHART_VERSION: '48.2.3'
  BLACKBOX_CONFIG_PATH: 'helm-values/prometheus-blackbox-exporter/other/config.yaml'
  BLACKBOX_CHART_VERSION: '8.3.0'
  DATADOG_CONFIG_PATH: 'helm-values/datadog-daemonset/other/config.yaml'
  DATADOG_CHART_VERSION: '3.33.10'
  DATADOG_RESOURCE_SIZE: 'prod' # test, prod-small, or prod
  INGRESS_NAMESPACE: 'ingress-nginx'
  DATADOG_NS: 'datadog'
  DATADOG_NAME: 'datadog'
  TOKENMASTER_CONFIG_PATH: 'helm-values/mic-gateway/nonprod/config.yaml'
  TOKENMASTER_NS: 'tokenmaster-nonprod'
  TOKENMASTER_NAME: 'tokenmaster'

  # ------------------------------- #
  #     Project related vars        #
  # ------------------------------- #

  # azureSubscription
  # AZ_SUBSCRIPTION: 'MIC-NPRD-ECE-WESTEUROPE-SBOD'
  AZ_SUBSCRIPTION: 'ECE-NPRD-NPRWEUB-SBOD'

  # Vault name.
  VAULT_NAME: 'sbod-prod'

# ------------------------------------------------------------------------------------------------------------------ #

parameters:
- name: REINSTALL_INGRESS
  displayName: (Re)Deploy Ingress Controller?
  type: boolean
  default: true
- name: REINSTALL_PROMETHEUS
  displayName: (Re)Deploy Prometheus?
  type: boolean
  default: false
- name: REINSTALL_DATADOG
  displayName: (Re)Deploy Datadog?
  type: boolean
  default: false
- name: REINSTALL_TOKENMASTER
  displayName: (Re)Deploy Tokenmaster?
  type: boolean
  default: false

trigger: none

# ------------------------------------------------------------------------------------------------------------------ #
#   Deployment stages                                                                                                #
# ------------------------------------------------------------------------------------------------------------------ #

stages:
# ----------------- CLUSTER 01 ----------------- #
# # Template reference - InfraApps deployment
- stage: deploy_aks_prod_weu_infra_apps
  displayName: Deploy AKS PROD WEU Infra Apps
  jobs:
    - job: "deploy_aks_nonprod_weu_infra_apps"
      pool:
        vmImage: 'ubuntu-latest'
      steps:
      - template: ../templates/aks-cluster-infra-apps.yml
        parameters:
          AzureSubs: ${{ variables.AZ_SUBSCRIPTION }}
          ClusterName: ${{ variables.CLUSTER_NAME_01 }}
          IngressConfigPath: ${{ variables.INGRESS_CONFIG_PATH }}
          IngressChartVersion: ${{ variables.INGRESS_CHART_VERSION }}
          monPromConfigPath: ${{ variables.MONITORING_CONFIG_PATH }}
          monPromChartVersion: ${{ variables.MONITORING_CHART_VERSION }}
          monBlackConfigPath: ${{ variables.BLACKBOX_CONFIG_PATH }}
          monBlackChartVersion: ${{ variables.BLACKBOX_CHART_VERSION }}
          ddConfigPath: ${{ variables.DATADOG_CONFIG_PATH }}
          ddChartVersion: ${{ variables.DATADOG_CHART_VERSION }}
          DatadogResourceSize: ${{ variables.DATADOG_RESOURCE_SIZE }}
          IngressNS: ${{ variables.INGRESS_NAMESPACE }}
          DatadogNS: ${{ variables.DATADOG_NS }}
          DatadogName: ${{ variables.DATADOG_NAME }}
          TokenmasterConfigPath: ${{ variables.TOKENMASTER_CONFIG_PATH }}
          TokenmasterNs: ${{ variables.TOKENMASTER_NS }}
          TokenmasterName: ${{ variables.TOKENMASTER_NAME }}
          reinstallIngress: ${{ parameters.REINSTALL_INGRESS }}
          reinstallProm: ${{ parameters.REINSTALL_PROMETHEUS }}
          reinstallDD: ${{ parameters.REINSTALL_DATADOG }}
          reinstallTokenmaster: ${{ parameters.REINSTALL_TOKENMASTER}}
          VaultName: ${{ variables.VAULT_NAME }}
# ----------------- CLUSTER 01 END -------------- #
# ------------------------------------------------------------------------------------------------------------------ #
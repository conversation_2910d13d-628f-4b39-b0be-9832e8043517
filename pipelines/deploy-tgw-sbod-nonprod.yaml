variables:
  allStages: true

trigger: none

stages:
  ### WEU
  - stage: mic_tgw_ece_sbod_ece_1_v1_weu
    displayName: Deploy SBOD MIC TGW NONPROD on WEU
    jobs:
      - template: "../templates/mic-gateway/gateway-deploy-job.yaml"
        parameters:
          jobName: job_deploy_sbod_tgw_ece_nprod_1_v1_weu
          akvName: "sbod-nonprod"
          subscription: "ECE-NPRD-NPRWEUB-SBOD"
          subscriptionVault: "ECE-NPRD-NPRWEUB-SBOD"
          aksClusterResourceGroup: "SBOD-NONPROD-K8S-V1-westeurope"
          aksClusterName: "sbod_ece_1_v1_weu"
          keyVaultSecretsFilter: "tokenmaster-sbod-clientid,tokenmaster-sbod-clientsecret,sp-sbod-clientid,sp-sbod-clientsecret"
          tokenGatewayNamespace: "mic-tgw-sbod-nonprod"
          applicationNamespace: "sbod-nonprod"
          helmReleaseName: "tgw-sbod-nonprod"
          micGatewayChartValueFile: "$(System.DefaultWorkingDirectory)/helm-values/mic-gateway/ECE/sbod-nonprod.yaml"
          micGatewayChartValueOverrides: "global.clientid=$(tokenmaster-sbod-clientid),global.clientsecret=$(tokenmaster-sbod-clientsecret)"
          micGatewayChartVersion: "3.23.1"
          deployProceed: true

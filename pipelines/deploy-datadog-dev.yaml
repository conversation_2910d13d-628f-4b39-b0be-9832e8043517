variables:
  allStages: true

trigger: none

stages:
  ### WEU
  - stage: datadog_ece_sbod_ece_1_v1_weu
    displayName: Deploy SBOD DATADOG DEV on WEU
    jobs:
      - template: "../templates/datadog/datadog-deploy-job.yaml"
        parameters:
          jobName: job_deploy_datadog_ece_dev_1_v1_weu
          akvName: "sbod-kv"
          subscription: "ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD"
          subscriptionVault: "ECE-DEV-DEVWEUB-SBOD"
          aksClusterResourceGroup: "SBOD-DEV-K8S-V1-westeurope"
          aksClusterName: "sbod_ece_1_v1_weu"
          keyVaultSecretsFilter: "tm-datadog-api-key,tm-datadog-app-key,sp-sbod-clientid,sp-sbod-clientsecret"
          datadogResourceSize: "nonprod"
          ingressNamespace: "ingress-nginx"
          datadogNamespace: "datadog"
          datadogHelmReleaseName: "datadog"
          datadogChartName: "datadog/datadog"
          datadogChartVersion: "3.80.0"
          datadogChartValueFile: "$(System.DefaultWorkingDirectory)/helm-values/datadog-daemonset/other/config.yaml"
          deployProceed: true

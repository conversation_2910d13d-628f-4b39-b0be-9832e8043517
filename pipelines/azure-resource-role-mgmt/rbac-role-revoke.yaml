trigger: none

pool:
  vmImage: ubuntu-latest

parameters:
  - name: environment
    displayName: Environment for the Azure Resource
    type: string
    values:
      - dev
      - nonprod
      - prod
  - name: assignedRole
  - name: assignedAzureResourceId

variables:
  - group: rbac-role-mgmt-${{ parameters.environment }}

steps:
  - task: AzureCLI@1
    displayName: 'Role Revoke'
    inputs:
      azureSubscription: rbac-role-mgmt-${{ parameters.environment }}
      scriptType: "bash"
      scriptLocation: "inlineScript"
      inlineScript: |
        az role assignment delete \
        --assignee $(assigneeObjectId) \
        --role ${{ parameters.assignedRole }} \
        --scope ${{ parameters.assignedAzureResourceId }}

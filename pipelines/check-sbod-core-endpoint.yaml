parameters:
  - name: endpoint
    displayName: Endpoint in the sbod-core
    type: string
    default: InFleet
    values:
      - InFleet
      - DeFleet
      - Event Notification
      - Get Digital Key Information
      - Manage Key
      - Prepare DK Sharing
      - Health Check

trigger: none

variables:
  ORI_URL: "https://sbod-dev.query.api.dvb.corpinter.net/core/api/v1/digitalkeys"
  AZ_SUBSCRIPTION: "ECE-NPRD-NPRWEUB-SBOD"
  VAULT_NAME: "sbod-dev"

  ${{ if eq(parameters.endpoint, 'InFleet') }}:
    ENDPOINT_NAME: "InFleet"
    METHOD: "POST"
    HOST_NAME: "${{ variables.ORI_URL }}/inFleet"
    REQUEST_PARAM_KEY: ""
    REQUEST_PARAM_VALUES: ""
    REQUEST_BODY: '{ \"vehicleId\" : \"19UUA765X7A035631\" }'
  ${{ if eq(parameters.endpoint, 'DeFleet') }}:
    ENDPOINT_NAME: "DeFleet"
    METHOD: "POST"
    HOST_NAME: "${{ variables.ORI_URL }}/deFleet"
    REQUEST_PARAM_KEY: ""
    REQUEST_PARAM_VALUES: ""
    REQUEST_BODY: '{ \"vehicleId\" : \"19UUA765X7A035631\" }'
  ${{ if eq(parameters.endpoint, 'Event Notification') }}:
    ENDPOINT_NAME: "Event Notification"
    METHOD: "POST"
    HOST_NAME: "${{ variables.ORI_URL }}/eventNotification"
    REQUEST_PARAM_KEY: ""
    REQUEST_PARAM_VALUES: ""
    REQUEST_BODY: '{ \"vehicleId\": \"WDDGF4HB9DR267526\", \"eventType\": \"INFLEETING_STARTED\"}'
  ${{ if eq(parameters.endpoint, 'Get Digital Key Information') }}:
    ENDPOINT_NAME: "Get Digital Key Information"
    METHOD: "POST"
    HOST_NAME: "${{ variables.ORI_URL }}/manageKey"
    REQUEST_PARAM_KEY: ""
    REQUEST_PARAM_VALUES: ""
    REQUEST_BODY: '{ \"vehicleId\": \"WDBEA51E0MB336315\", \"fmsSharingId\": \"5bc05ff5-9ae4-4b8e-9d16-62593c401ddb\" }'
  ${{ if eq(parameters.endpoint, 'Manage Key') }}:
    ENDPOINT_NAME: "Manage Key"
    METHOD: "POST"
    HOST_NAME: "${{ variables.ORI_URL }}/manageKey"
    REQUEST_PARAM_KEY: ""
    REQUEST_PARAM_VALUES: ""
    REQUEST_BODY: '{ \"vehicleId\": \"WDBEA51E0MB336315\", \"fmsSharingId\": \"5bc05ff5-9ae4-4b8e-9d16-62593c401ddb\", \"action\": \"TERMINATE\" }'
  ${{ if eq(parameters.endpoint, 'Prepare DK Sharing') }}:
    ENDPOINT_NAME: "Prepare DK Sharing"
    METHOD: "POST"
    HOST_NAME: "${{ variables.ORI_URL }}/prepareDKSharing"
    REQUEST_PARAM_KEY: ""
    REQUEST_PARAM_VALUES: ""
    REQUEST_BODY: '{ \"vehicleId\": \"WDBEA51E0MB336315\", \"accountIdHash\": \"C8645830202EFEB53427A6D75F15C85E78A5195307E2351858349AB9\", \"fmsSharingId\": \"5bc05ff5-9ae4-4b8e-9d16-62593c401ddb\", \"dkEntitlements\": { \"friendlyName\": \"Jons Phone\", \"rights\": 0, \"notBefore\": \"2024-04-23T06:27:55.197Z\", \"notAfter\": \"2024-04-23T06:27:55.197Z\" } }'
  ${{ if eq(parameters.endpoint, 'Health Check') }}:
    ENDPOINT_NAME: "Health Check"
    METHOD: "GET"
    HOST_NAME: "${{ variables.ORI_URL }}/healthcheck"
    REQUEST_PARAM_KEY: ""
    REQUEST_PARAM_VALUES: ""
    REQUEST_BODY: ""

stages:
  - stage: check_sbod_core_digital_key_endpoints
    displayName: Check SBOD Core Digital Key Endpoint
    jobs:
      - job: "check_digital_key_endpoint"
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - ${{ each REQUEST_PARAM_VALUE in split(variables.REQUEST_PARAM_VALUES, ',') }}:
              - template: ../templates/cron-jobs/cron-call-request-template.yaml
                parameters:
                  azureSubscription: ${{ variables.AZ_SUBSCRIPTION }}
                  vaultName: ${{ variables.VAULT_NAME }}
                  endPointName: ${{ variables.ENDPOINT_NAME }}
                  method: ${{ variables.METHOD }}
                  hostName: ${{ variables.HOST_NAME }}
                  requestParamKey: ${{ variables.REQUEST_PARAM_KEY }}
                  requestParam: ${{ REQUEST_PARAM_VALUE }}
                  requestBody: ${{ variables.REQUEST_BODY }}

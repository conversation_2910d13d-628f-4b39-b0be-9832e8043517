trigger: none

variables:
  AZURE_SUBSCRIPTION: ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD
  AZURE_K8S_RESOURCE_GROUP: SBOD-DEV-K8S-V1-westeurope
  KUBERNETES_CLUSTER: sbod_ece_1_v1_weu
  NAMESPACE: linkerd
  AKV_NAME: sbod-dev

  CRDS_CHART_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/service-mesh/linkerd-crds
  CRDS_CONFIG_FILE_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/service-mesh/linkerd-crds/values.yaml
  CRDS_CHART_RELEASE_NAME: service-mesh-linkerd-crds

  CONTROL_PLANE_CHART_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/service-mesh/linkerd-control-plane
  CONTROL_PLANE_CONFIG_FILE_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/service-mesh/linkerd-control-plane/values.yaml
  CONTROL_PLANE_CHART_RELEASE_NAME: service-mesh-linkerd-control-plane

  VIZ_CHART_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/service-mesh/linkerd-viz
  VIZ_CONFIG_FILE_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/service-mesh/linkerd-viz/values.yaml
  VIZ_CHART_RELEASE_NAME: service-mesh-linkerd-viz
  VIZ_NAMESPACE: linkerd-viz

stages:
  - stage: deploy_service_mesh_linkerd_control_plane
    displayName: Deploy Service Mesh Linkerd Control Plane
    jobs:
      - job: "deploy_service_mesh_linkerd_control_plane"
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - template: ../templates/service-mesh/linkerd/deploy-linkerd-crds-steps.yaml # Template reference
            parameters:
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION }}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP }}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER }}
              namespace: ${{ variables.NAMESPACE }}
              chartPath: ${{ variables.CRDS_CHART_PATH }}
              configFilePath: ${{ variables.CRDS_CONFIG_FILE_PATH }}
              chartReleaseName: ${{ variables.CRDS_CHART_RELEASE_NAME }}
          - template: ../templates/service-mesh/linkerd/deploy-linkerd-control-plane-steps.yaml
            parameters:
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION }}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP }}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER }}
              namespace: ${{ variables.NAMESPACE }}
              chartPath: ${{ variables.CONTROL_PLANE_CHART_PATH }}
              configFilePath: ${{ variables.CONTROL_PLANE_CONFIG_FILE_PATH }}
              chartReleaseName: ${{ variables.CONTROL_PLANE_CHART_RELEASE_NAME }}
              akvName: ${{ variables.AKV_NAME }}
          - template: ../templates/service-mesh/linkerd/deploy-linkerd-viz-steps.yaml # Template reference
            parameters:
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION }}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP }}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER }}
              namespace: ${{ variables.VIZ_NAMESPACE }}
              chartPath: ${{ variables.VIZ_CHART_PATH }}
              configFilePath: ${{ variables.VIZ_CONFIG_FILE_PATH }}
              chartReleaseName: ${{ variables.VIZ_CHART_RELEASE_NAME }}

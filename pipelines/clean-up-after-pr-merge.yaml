trigger: none
pr: none

resources:
  webhooks:
    - webhook: webhook
      connection: ado-pr-sc

variables:
  AZURE_SUBSCRIPTION: ECE-NPRD-NPRWEUB-SBOD
  AZURE_K8S_RESOURCE_GROUP: SBOD-NONPROD-K8S-V1-westeurope
  KUBERNETES_CLUSTER: sbod_ece_1_v1_weu
  HELM_VERSION: v3.12.3
  CHART_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/sbod-core
  CONFIG_FILE_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/sbod-core/dev.yaml
  VAULT_NAME: "sbod-dev"
  REPO_NAME: "sbod-core"
  NAMESPACE: "sbod-dev"
  ACR_NAME: "sbodacrdev"

pool:
  vmImage: ubuntu-latest

stages:
- stage: get_branch_name_stage
  displayName: Get clean up branch name
  jobs:
    - job: "get_branch_name_job"
      steps:
        - bash: |
            NAME=${{ parameters.webhook.resource.sourceRefName }}
            REPLACESTR="refs/heads/features/"
            STR=""

            # Replace the string in NAME with REPLACESTR with STR
            NAME="${NAME/$REPLACESTR/$STR}"

            echo "Feature branch to clean up: $NAME"
            echo "##vso[task.setvariable variable=branchName;isOutput=true]$NAME"
          name: getBranchNameStep

- stage: clean_up_kubernetes_deployment
  dependsOn: get_branch_name_stage
  displayName: Clean up kubernetes deployment
  variables:
    branchToRemove: $[ stageDependencies.get_branch_name_stage.get_branch_name_job.outputs['getBranchNameStep.branchName'] ]
  jobs:
    - job: "clean_up_kubernetes_deployment"
      steps:
        - template: ../templates/cleanup-job/cleanup-kubernetes-deployment.yaml
          parameters:
            branchName: $(branchToRemove)
            azureSubscription: ${{ variables.AZURE_SUBSCRIPTION}}
            azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP}}
            kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER}}
            keyvaultName: ${{ variables.VAULT_NAME }}

- stage: clean_up_azure_artifacts
  dependsOn: get_branch_name_stage
  displayName: Clean up azure artifacts
  variables:
    branchToRemove: $[ stageDependencies.get_branch_name_stage.get_branch_name_job.outputs['getBranchNameStep.branchName'] ]
  jobs:
    - job: "clean_up_azure_artifacts"
      steps:
        - template: ../templates/cleanup-job/cleanup-azure-artifacts.yaml
          parameters:
            branchName: $(branchToRemove)
            keyvaultName: ${{ variables.VAULT_NAME }}
            azureSubscription: ${{ variables.AZURE_SUBSCRIPTION}}

- stage: clean_up_acr_repositories
  dependsOn: get_branch_name_stage
  displayName: Clean up ACR repositories
  variables:
    branchToRemove: $[ stageDependencies.get_branch_name_stage.get_branch_name_job.outputs['getBranchNameStep.branchName'] ]
  jobs:
    - job: "clean_up_acr_repositories"
      steps:
        - template: ../templates/cleanup-job/cleanup-acr-repositories.yaml
          parameters:
            branchName: $(branchToRemove)
            azureSubscription: ${{ variables.AZURE_SUBSCRIPTION}}
            acrName: ${{ variables.ACR_NAME }}

parameters:
  - name: environment
    displayName: Release to environment
    type: string
    default: dev
    values:
      - dev
      - nonprod
      - prod
  - name: buildAndDeploy
    displayName: Build and deploy [If false then build only]
    type: boolean
    default: false

variables:
  ${{ if eq( ${{ parameters.environment }}, 'dev') }}:
    COMMON_RELEASE_NAME: "sbod-common-dev"
    IMAGE_TAG: "latest"
    CHART_RELEASE_NAME: ""
    BRANCH_NAME: "main"
    AZURE_SUBSCRIPTION: ECE-NPRD-NPRWEUB-SBOD
    AZURE_K8S_RESOURCE_GROUP: SBOD-NONPROD-K8S-V1-westeurope
    KUBERNETES_CLUSTER: sbod_ece_1_v1_weu
    HELM_VERSION: v3.12.3
    CHART_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/sbod-ext
    CONFIG_FILE_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/sbod-ext/dev.yaml
    VAULT_NAME: "sbod-dev"
    REPO_NAME: "sbod-ext"
    NAMESPACE: "sbod-dev"
  ${{ if eq( ${{ parameters.environment }}, 'nonprod') }}:
    COMMON_RELEASE_NAME: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]
    IMAGE_TAG: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]
    CHART_RELEASE_NAME: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]
    BRANCH_NAME: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]
    AZURE_SUBSCRIPTION: ECE-NPRD-NPRWEUB-SBOD
    AZURE_K8S_RESOURCE_GROUP: SBOD-NONPROD-K8S-V1-westeurope
    KUBERNETES_CLUSTER: sbod_ece_1_v1_weu
    HELM_VERSION: v3.12.3
    CHART_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-charts/sbod-ext
    CONFIG_FILE_PATH: $(Build.SourcesDirectory)/sbod-infrastructure/helm-values/sbod-ext/dev.yaml
    VAULT_NAME: "sbod-dev"
    REPO_NAME: "sbod-ext"
    NAMESPACE: "sbod-nonprod"
  ${{ if eq( ${{ parameters.environment }}, 'prod') }}:
    COMMON_RELEASE_NAME: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]
    IMAGE_TAG: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]
    CHART_RELEASE_NAME: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]
    BRANCH_NAME: $[replace(variables['Build.SourceBranch'], 'refs/heads/features/', '')]


trigger:
  - none

resources:
  repositories:
    - repository: sbod-core
      type: git
      name: "sbod/sbod-core" # repository in Azure DevOps
      ref: refs/heads/main
      trigger:
        - none
    - repository: sbod-ext
      type: git
      name: "sbod/sbod-ext" # repository in Azure DevOps
      ref: refs/heads/main
      trigger:
        - none
    - repository: sbod-common
      type: git
      name: "sbod/sbod-common" # repository in Azure DevOps
      ref: refs/heads/main
      trigger:
        - none

pool:
  vmImage: ubuntu-latest

stages:
  - stage: build_and_deploy_sbod_core
    displayName: Build and deploy SBOD core
    jobs:
      - job: "build_and_deploy_sbod_core"
        steps:
          - template: ../templates/build_and_deploy_feature_branch.yaml
            parameters:
              commonReleaseName: $(COMMON_RELEASE_NAME)
              imageTag: $(IMAGE_TAG)
              chartReleaseName: $(CHART_RELEASE_NAME)
              branchName: $(BRANCH_NAME)
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION}}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP}}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER}}
              helmVersion: ${{ variables.HELM_VERSION}}
              chartPath: ${{ variables.CHART_PATH}}
              configFilePath: ${{ variables.CONFIG_FILE_PATH}}
              keyvaultName: ${{ variables.VAULT_NAME }}
              repoName: sbod-core
              namespace: ${{ variables.NAMESPACE }}

  - stage: build_and_deploy_sbod_ext
    displayName: Build and deploy SBOD ext
    jobs:
      - job: "build_and_deploy_sbod_ext"
        steps:
          - template: ../templates/build_and_deploy_feature_branch.yaml
            parameters:
              commonReleaseName: $(COMMON_RELEASE_NAME)
              imageTag: $(IMAGE_TAG)
              chartReleaseName: $(CHART_RELEASE_NAME)
              branchName: $(BRANCH_NAME)
              azureSubscription: ${{ variables.AZURE_SUBSCRIPTION}}
              azureK8sResourceGroup: ${{ variables.AZURE_K8S_RESOURCE_GROUP}}
              kubernetesCluster: ${{ variables.KUBERNETES_CLUSTER}}
              helmVersion: ${{ variables.HELM_VERSION}}
              chartPath: ${{ variables.CHART_PATH}}
              configFilePath: ${{ variables.CONFIG_FILE_PATH}}
              keyvaultName: ${{ variables.VAULT_NAME }}
              repoName: sbod-ext
              namespace: ${{ variables.NAMESPACE }}


  - stage: release_tag
    displayName: Release snapshot for PRODUCTION
    condition: and(succeeded(), eq(${{ parameters.environment }}, 'prod'))
    dependsOn:
      - build_and_deploy_sbod_core
      - build_and_deploy_sbod_ext
    jobs:
      - job: "release_tag"
        steps:
          - bash: echo "this is release tag"

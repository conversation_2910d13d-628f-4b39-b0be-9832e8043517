trigger: none

schedules:
  - cron: "0 0 * * *"
    always: true
    branches:
      include:
        - main

pool:
  vmImage: ubuntu-latest

resources:
  repositories:
    - repository: sbod-core
      type: git
      name: "sbod/sbod-core"
    - repository: sbod-ext
      type: git
      name: "sbod/sbod-ext"

variables:
- group: sdp

jobs:
- job: "perform_sonarqube_scan_sbod_core"
  steps:
    - template: ../templates/security/sonarqube/sonarqube-scan.yaml
      parameters:
        repoName: "sbod-core"
        projectKey: "SBODCORE"
        projectName: "SBODCORE"
        sonarqubeToken: $(sonarqube-core-token)
- job: "perform_sonarqube_scan_sbod_ext"
  steps:
    - template: ../templates/security/sonarqube/sonarqube-scan.yaml
      parameters:
        repoName: "sbod-ext"
        projectKey: "SBODEXT"
        projectName: "SBODEXT"
        sonarqubeToken: $(sonarqube-ext-token)

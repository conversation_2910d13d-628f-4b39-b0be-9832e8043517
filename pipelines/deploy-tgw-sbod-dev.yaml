variables:
  allStages: true

trigger: none

stages:
  ### WEU
  - stage: mic_tgw_ece_sbod_ece_1_v1_weu
    displayName: Deploy SBOD MIC TGW DEV on WEU
    jobs:
      - template: "../templates/mic-gateway/gateway-deploy-job.yaml"
        parameters:
          jobName: job_deploy_sbod_tgw_ece_dev_1_v1_weu
          akvName: "sbod-kv"
          subscription: "ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD"
          subscriptionVault: "ECE-DEV-DEVWEUB-SBOD"
          aksClusterResourceGroup: "SBOD-DEV-K8S-V1-westeurope"
          aksClusterName: "sbod_ece_1_v1_weu"
          keyVaultSecretsFilter: "tokenmaster-sbod-clientid,tokenmaster-sbod-clientsecret,sp-sbod-clientid,sp-sbod-clientsecret"
          tokenGatewayNamespace: "mic-tgw-sbod-dev"
          applicationNamespace: "sbod-dev"
          helmReleaseName: "tgw-sbod-dev"
          micGatewayChartValueFile: "$(System.DefaultWorkingDirectory)/helm-values/mic-gateway/ECE/sbod-dev.yaml"
          micGatewayChartValueOverrides: "global.clientid=$(tokenmaster-sbod-clientid),global.clientsecret=$(tokenmaster-sbod-clientsecret)"
          micGatewayChartVersion: "3.23.1"
          deployProceed: true

parameters:
  - name: application
    displayName: Environment to for DORA
    type: string
    values:
      - sbod-core
      - sbod-ext
      - both

trigger:
  - none

resources:
  repositories:
    - repository: sbod-core
      type: git
      name: "sbod/sbod-core"
    - repository: sbod-ext
      type: git
      name: "sbod/sbod-ext"

pool:
  vmImage: 'ubuntu-latest'
  
stages:  
  - stage: dora_sbod_core
    displayName: DORA SBOD core
    condition: or(eq('${{ parameters.application }}', 'sbod-core'), eq('${{ parameters.application }}', 'both'))
    jobs:
      - job: "dora_sbod_core"
        steps:
          - template: ../templates/dora/sbod-dorawebhook.yaml
            parameters:
              repositoryUrl: 'https://<EMAIL>/daimler-mic/sbod/_git/sbod-core'
              application: sbod-core

  - stage: dora_sbod_ext
    displayName: DORA SBOD ext
    condition: or(eq('${{ parameters.application }}', 'sbod-ext'), eq('${{ parameters.application }}', 'both'))
    jobs:
      - job: "dora_sbod_ext"
        steps:
          - template: ../templates/dora/sbod-dorawebhook.yaml
            parameters:
              repositoryUrl: 'https://<EMAIL>/daimler-mic/sbod/_git/sbod-ext'
              application: sbod-ext
###
POST https://ssoalpha.dvb.corpinter.net/v1/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic A619EE50-D274-44DD-98A9-2C62FD766F75 _2N5teI8R1L960~7J3W4EnhGHsl.O-Ba

scope=openid profile email mic:env:prod groups audience:server:client_id:DAIVBADM_MICTM_EMEA_PROD_00648&grant_type=client_credentials

> {% client.global.set("auth_token", response.body.access_token); %}

### [MBOS Portal] - Get User Info
GET https://dre.query.api.dvb.corpinter.net/organizations/sbod
Authorization: Bearer {{auth_token}}

### [MBOS Portal] - Get User Info
GET https://dre.query.api.dvb.corpinter.net/organizations/sbod/role-assignments
Authorization: Bearer {{auth_token}}

### [MBOS Portal] - Change Role to whole team
PUT https://dre.query.api.dvb.corpinter.net/organizations/sbod/role-assignments
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "owners": [
    {"id": "A619EE50-D274-44DD-98A9-2C62FD766F75"},
    {"id": "YOKYEOW"},
    {"id": "PANGCHE"},
    {"id": "KENGTEO"},
    {"id": "SHENTEO"},
    {"id": "SIMSTEF"},
    {"id":  "BAORLIM"}
  ],
  "members": [],
  "organization_readers": []
}

###
GET https://dre.query.api.dvb.corpinter.net/organizations/sbod/apis/sbod/role-assignments
Authorization: Bearer {{auth_token}}

###
PUT https://dre.query.api.dvb.corpinter.net/organizations/sbod/apis/sbod/role-assignments HTTP/1.1
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "owners": [
    {"id": "A619EE50-D274-44DD-98A9-2C62FD766F75"},
    {"id": "YOKYEOW"},
    {"id": "PANGCHE"},
    {"id": "KENGTEO"},
    {"id": "SHENTEO"},
    {"id": "SIMSTEF"},
    {"id":  "BAORLIM"}
  ],
  "maintainers": [],
  "readers": []
}


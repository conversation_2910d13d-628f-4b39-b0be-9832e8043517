# github.com/go-task/task

version: '3'

vars:
  DB_CONTAINER_NAME: "postgres-sbod"
  DB_DATABASE_NAME: "db"
  DB_VOLUME_NAME: "pgvol"
  DB_NETWORK: "pgnet"
  DB_ADMIN_USER: "postgres"
  DB_ADMIN_PASS: "postgres"
  DB_SCHEMA_CORE_NAME: "sbod_core"
  DB_SCHEMA_EXT_NAME: "sbod_ext"

tasks:
  createCoreSchema:
    desc: |-
      Create Core schema on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
          -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} \
          -c "CREATE USER {{.DB_SCHEMA_CORE_NAME}} PASSWORD '{{.DB_SCHEMA_CORE_NAME}}';
          CREATE SCHEMA IF NOT EXISTS {{.DB_SCHEMA_CORE_NAME}};
          GRANT CREATE, USAGE ON SCHEMA {{.DB_SCHEMA_CORE_NAME}} TO {{.DB_SCHEMA_CORE_NAME}};
          ALTER ROLE {{.DB_SCHEMA_CORE_NAME}} SET search_path={{.DB_SCHEMA_CORE_NAME}};
          ALTER DEFAULT PRIVILEGES IN SCHEMA {{.DB_SCHEMA_CORE_NAME}} GRANT ALL ON TABLES TO {{.DB_SCHEMA_CORE_NAME}}, {{.DB_ADMIN_USER}};
          ALTER DEFAULT PRIVILEGES IN SCHEMA {{.DB_SCHEMA_CORE_NAME}} GRANT ALL ON SEQUENCES TO {{.DB_SCHEMA_CORE_NAME}}, {{.DB_ADMIN_USER}};"
        silent: false

  createExtSchema:
    desc: |-
      Create Ext schema on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
          -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} \
          -c "CREATE USER {{.DB_SCHEMA_EXT_NAME}} PASSWORD '{{.DB_SCHEMA_EXT_NAME}}';
          CREATE SCHEMA IF NOT EXISTS {{.DB_SCHEMA_EXT_NAME}};
          GRANT CREATE, USAGE ON SCHEMA {{.DB_SCHEMA_EXT_NAME}} TO {{.DB_SCHEMA_EXT_NAME}};
          ALTER ROLE {{.DB_SCHEMA_EXT_NAME}} SET search_path={{.DB_SCHEMA_EXT_NAME}};
          ALTER DEFAULT PRIVILEGES IN SCHEMA {{.DB_SCHEMA_EXT_NAME}} GRANT ALL ON TABLES TO {{.DB_SCHEMA_EXT_NAME}}, {{.DB_ADMIN_USER}};
          ALTER DEFAULT PRIVILEGES IN SCHEMA {{.DB_SCHEMA_EXT_NAME}} GRANT ALL ON SEQUENCES TO {{.DB_SCHEMA_EXT_NAME}}, {{.DB_ADMIN_USER}};"
        silent: false

  dropCoreSchema:
    desc: |-
      Drop Core schema on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
          -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} \
          -c "DROP SCHEMA IF EXISTS {{.DB_SCHEMA_CORE_NAME}} CASCADE; DROP USER IF EXISTS {{.DB_SCHEMA_CORE_NAME}}"
        silent: true

  dropExtSchema:
    desc: |-
      Drop Ext schema on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
          -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} \
          -c "DROP SCHEMA IF EXISTS {{.DB_SCHEMA_EXT_NAME}} CASCADE; DROP USER IF EXISTS {{.DB_SCHEMA_EXT_NAME}}"
        silent: true
 
  dropAllCoreSequences:
    desc: |-
      Drop all sequences in Core schema on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} -At \
            -c "select concat('drop sequence if exists ', sequence_schema, '.', sequence_name, ' cascade;') from information_schema.sequences where sequence_catalog = current_database() and sequence_schema = '{{.DB_SCHEMA_CORE_NAME}}';" \
            | podman exec -i -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} --echo-all
        silent: true

  dropAllExtSequences:
    desc: |-
      Drop all sequences in Ext schema on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} -At \
            -c "select concat('drop sequence if exists ', sequence_schema, '.', sequence_name, ' cascade;') from information_schema.sequences where sequence_catalog = current_database() and sequence_schema = '{{.DB_SCHEMA_EXT_NAME}}';" \
            | podman exec -i -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} --echo-all
        silent: true

  dropAllCoreTables:
    desc: |-
      Drop all tables in Core schema on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} -At \
            -c "select concat('drop table if exists ', table_schema, '.', table_name, ' cascade;') from information_schema.tables where table_catalog = current_database() and table_schema = '{{.DB_SCHEMA_CORE_NAME}}';" \
            | podman exec -i -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} --echo-all
        silent: true

  dropAllExtTables:
    desc: |-
      Drop all tables in Ext schema on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} -At \
            -c "select concat('drop table if exists ', table_schema, '.', table_name, ' cascade;') from information_schema.tables where table_catalog = current_database() and table_schema = '{{.DB_SCHEMA_EXT_NAME}}';" \
            | podman exec -i -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} --echo-all
        silent: true

  shellCore:
    desc: |-
      Start SQL Shell for Core schema.
    cmds:
      - cmd: |-
          podman exec -ti -e PGPASSWORD={{.DB_ADMIN_PASS}} \
          -e PGOPTIONS=--search_path={{.DB_SCHEMA_CORE_NAME}} {{.DB_CONTAINER_NAME}} psql \
          -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}}
        silent: true

  shellExt:
    desc: |-
      Start SQL Shell for Ext schema.
    cmds:
      - cmd: |-
          podman exec -ti -e PGPASSWORD={{.DB_ADMIN_PASS}} \
          -e PGOPTIONS=--search_path={{.DB_SCHEMA_EXT_NAME}} {{.DB_CONTAINER_NAME}} psql \
          -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}}
        silent: true

  clearAllCoreTables:
    desc: |-
      Truncate all tables in Core schema (only data is removed).
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} -At \
            -c "select concat('truncate table ', table_schema, '.', table_name, ' cascade;') from information_schema.tables where table_catalog = current_database() and table_schema = '{{.DB_SCHEMA_CORE_NAME}}';" \
            | podman exec -i -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} --echo-all
        silent: true

  clearAllExtTables:
    desc: |-
      Truncate all tables in Ext schema (only data is removed).
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} -At \
            -c "select concat('truncate table ', table_schema, '.', table_name, ' cascade;') from information_schema.tables where table_catalog = current_database() and table_schema = '{{.DB_SCHEMA_EXT_NAME}}';" \
            | podman exec -i -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
            -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} --echo-all
        silent: true

  createDigitalKeyView:
    desc: |-
      Create Digital Key View on local postgres.
    cmds:
      - cmd: |-
          podman exec -e PGPASSWORD={{.DB_ADMIN_PASS}} {{.DB_CONTAINER_NAME}} psql \
          -U {{.DB_ADMIN_USER}} -d {{.DB_DATABASE_NAME}} \
          -c "CREATE OR REPLACE VIEW sbod_ext.digital_key_view AS
              SELECT dk.vehicle_identifier, dkc.certificate FROM sbod_core.digital_key_certificate dkc
              LEFT JOIN sbod_core.digital_key dk on dkc.digital_key_id = dk.id and dkc.status='ACTIVE';"
        silent: false

# github.com/go-task/task

version: "3"

vars:
  WIREMOCK_IMAGE: "registry-emea.app.corpintra.net/dockerhub/wiremock/wiremock"
  WIREMOCK_IMAGE_VERSION: "2.35.0"
  WIREMOCK_CONTAINER_NAME: "sbod-wiremock"
  WIREMOCK_PORT: "8080"
  WIREMOCK_MY_PORT: "8089"
  WIREMOCK_MOUNT_PATH: "./wiremock"

tasks:
  setupWiremock:
    desc: |-
      Pull official Wiremock image, setup and run single node within a container locally.
    cmds:
      - docker pull {{.WIREMOCK_IMAGE}}:{{.WIREMOCK_IMAGE_VERSION}}
  runWiremock:
    desc: |-
      Run Wiremock locally and mount your directory to Wiremock mapping directory
    cmds:
      - docker run -it --rm -p {{.WIREMOCK_MY_PORT}}:{{.WIREMOCK_PORT}} --name {{.WIREMOCK_CONTAINER_NAME}} -v {{.WIREMOCK_MOUNT_PATH}}:/home/<USER>/wiremock:{{.WIREMOCK_IMAGE_VERSION}}

# github.com/go-task/task

version: '3'

vars:
  KAF<PERSON>_IMAGE: "bitnami/kafka"
  KAFKA_IMAGE_VERSION: "latest"
  KAFKA_CONTAINER_NAME: "kafka-server"
  KAFKA_NETWORK: "app-tier"

tasks:
  setupAll:
    desc: |-
      Pull Kafka image from bitnami, setup and run single node within a container locally.
    cmds:
      - podman pull {{.KAFKA_IMAGE}}:{{.KAFKA_IMAGE_VERSION}}
      - task: setupNetwork
      - |-
        podman run -d --name={{.KAFKA_CONTAINER_NAME}} --hostname={{.KAF<PERSON>_CONTAINER_NAME}} \
          --net={{.KAFKA_NETWORK}} -p 9094:9094 \
          -e KAFKA_CFG_NODE_ID=0 \
          -e KAFKA_CFG_PROCESS_ROLES=controller,broker \
          -e KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094 \
          -e KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,EXTERNAL:PLAINTEXT \
          -e KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,EXTERNAL://localhost:9094 \
          -e KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka-server:9093 \
          -e KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER \
          -e ALLOW_PLAINTEXT_LISTENER=yes \
          {{.KAFKA_IMAGE}}:{{.KAFKA_IMAGE_VERSION}}
      - podman ps -f name={{.KAFKA_CONTAINER_NAME}}
      - cmd: echo 'Start the Kafka Server and connect to localhost:9094 with user'
        silent: true
    status:
      - test `podman container ls -aqf "name={{.KAFKA_CONTAINER_NAME}}" | wc -l` == 1
  setupNetwork:
    desc: |-
      Setup bridge network for Kafka Bitnami
    cmds:
      - podman network create {{.KAFKA_NETWORK}} --driver bridge
    status:
      - test `podman network ls -qf "name={{.KAFKA_NETWORK}}" | wc -l` == 1
  resetContainer:
    desc: |-
      Remove the Kafka container.
    cmds:
      - podman stop {{.KAFKA_CONTAINER_NAME}}
      - podman rm {{.KAFKA_CONTAINER_NAME}}
  removeFrompodman:
    desc: |-
      Remove the kafka Image & Network.
    cmds:
      - task: resetContainer
      - podman network rm {{.KAFKA_NETWORK}}
  restartContainer:
    desc: |-
      Restart the existing Kafka container.
    cmds:
      - podman restart {{.KAFKA_CONTAINER_NAME}}
  stopContainer:
    desc: |-
      Stop the running Kafka container.
    cmds:
      - podman stop {{.KAFKA_CONTAINER_NAME}}

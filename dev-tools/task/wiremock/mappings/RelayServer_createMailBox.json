{"mappings": [{"request": {"method": "POST", "urlPattern": "/relay/v1/m", "headers": {"Content-Type": {"equalTo": "application/json"}, "deviceClaim": {"matches": "[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}"}}, "bodyPatterns": [{"matchesJsonPath": "$.displayInformation"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"urlLink": "https://relayserver.example.com/m/12345678-90ab-cdef-1234-567890abcdef", "isPushNotificationSupported": true}}}, {"request": {"method": "POST", "urlPattern": "/relay/v1/m", "headers": {"Content-Type": {"equalTo": "application/json"}, "deviceClaim": {"absent": true}}}, "response": {"status": 400, "headers": {"Content-Type": "application/json"}, "jsonBody": {"error": "Bad Request", "message": "Missing required header: deviceClaim"}}}, {"request": {"method": "POST", "urlPattern": "/relay/v1/m", "headers": {"Content-Type": {"equalTo": "application/json"}, "deviceClaim": {"matches": "[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}"}}, "bodyPatterns": [{"matchesJsonPath": "$.payload", "absent": "$.displayInformation"}]}, "response": {"status": 400, "headers": {"Content-Type": "application/json"}, "jsonBody": {"error": "Bad Request", "message": "Missing required field: displayInformation"}}}]}
version: '3'

vars:
  PGADMIN_CONTAINER_NAME: "pgadmin-sbod"
  PGADMIN_PASSWORD: "postgres"
  PGADMIN_PORT: "5050"
  DB_NETWORK: "pgnet"
  PGADMIN_EMAIL: "<EMAIL>"  # Added default email

tasks:
  runPgAdmin:
    desc: Pull and run PgAdmin container
    cmds:
      - cmd: |-
          podman run -d \
            --name {{.PGADMIN_CONTAINER_NAME}} \
            --network {{.DB_NETWORK}} \
            -e PGADMIN_DEFAULT_EMAIL={{.PGADMIN_EMAIL}} \
            -e PGADMIN_DEFAULT_PASSWORD={{.PGADMIN_PASSWORD}} \
            -p {{.PGADMIN_PORT}}:80 \
            docker.io/dpage/pgadmin4:latest
        silent: false

  stopPgAdmin:
    desc: Stop and remove PgAdmin container
    cmds:
      - cmd: podman stop {{.PGADMIN_CONTAINER_NAME}}
        silent: true
      - cmd: podman rm {{.PGADMIN_CONTAINER_NAME}}
        silent: true

  status:
    desc: Check PgAdmin container status
    cmds:
      - cmd: podman container inspect {{.PGADMIN_CONTAINER_NAME}}
        silent: false
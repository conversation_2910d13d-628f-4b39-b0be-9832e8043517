version: '3'

includes:
  container: ./ContainerTask.yaml
  database: ./DatabaseTask.yaml
  kafka: ./KafkaTask.yaml

tasks:
  setup:
    desc: Setup the complete environment (container and database)
    cmds:
      - task: container:setupAll
      - task: kafka:setupAll
      - sleep 3  # Wait for postgres to be ready
      - task: database:createCoreSchema
      - task: database:createExtSchema
      
    status:
      - podman container inspect postgres-sbod >/dev/null 2>&1

  teardown:
    desc: Teardown the complete environment
    cmds:
      - task: database:dropCoreSchema
      - task: database:dropAllCoreSequences
      - task: database:dropAllCoreTables
      - task: database:dropExtSchema
      - task: database:dropAllExtSequences
      - task: database:dropAllExtTables
      - task: container:resetContainer
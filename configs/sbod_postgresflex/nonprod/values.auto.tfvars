# © [2021] Mercedes-Benz AG. All rights reserved
# --------------------------------------------------------------------------------------------------
# AKS Cluster variables
# --------------------------------------------------------------------------------------------------
location = "westeurope"

env_name = "nonprod"

region_name = "weu"

rg_name_prefix = "SBOD-db"

app_name = "sbod"

pg_sku_name = "B_Standard_B1ms"

pg_version = "15"

pg_geo_redundant_backup_enabled = "false"

keyvault_id = "/subscriptions/e30c9817-212f-421b-bbdb-d831961e90c8/resourceGroups/ece-nprweub-sbod-rg/providers/Microsoft.KeyVault/vaults/sbod-keepass"

# Service principal of ADO's AzureRM service connection
service_principal_object_id = "c8048594-0076-48a7-b96a-2b1c1d96c8cd"

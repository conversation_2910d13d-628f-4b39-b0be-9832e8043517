# © [2021] Mercedes-Benz AG. All rights reserved
# --------------------------------------------------------------------------------------------------
# AKS Cluster variables
# --------------------------------------------------------------------------------------------------
tag_stage = "dev"

tag_cluster_version = "v1"

deploy_to_azurechina = false

location = "westeurope"

shortname = "weu"

rg_name = "SBOD-DEV-K8S-V1"

# Must be the same for all Environments
rg_net_name = "Network-SBOD-Services-PublicIPs"

cl_name = "sbod_ece_1_v1"

dns_prefix = "sbod-ece-cluster-1-v1"

vm_size = "Standard_D4as_v5"

agent_count = 3

kubernetes_version = "1.30.0"

# --------------------------------------------
# Can/Should be left as is...
# --------------------------------------------
vnet_name = "k8s-dev-vnet-v1"

vnet_ip = "10.0.0.0/8"

subnet_name = "k8s-dev-subnet-v1"

subnet_ip = "**********/16"

max_pods_per_node = "110"

# --------------------------------------------------------------------------------------------------


# --------------------------------------------------------------------------------------------------
# !!! Give the name of existing Log Analytic group and Workspace.
# If group/workspace does NOT exist, new will NOT be created and script will fail.
# Should be unique for all clusters in all regions!!!
# --------------------------------------------------------------------------------------------------
log_analytics_workspace_name = "ece-prdweua-sbod-laworkspace"

log_analytics_workspace_rg = "ece-prdweua-sbod-laworkspace-rg"

# --------------------------------------------------------------------------------------------------


# --------------------------------------------------------------------------------------------------
# IP Address variables. Domain name must be unique!!!
# Public IP deploys with name: cl_name_shortname_IP in the location: var.location
# CHANGING cl_name or shortname or location WILL cause a re-deployment of current publicIP and AKS!!!
# --------------------------------------------------------------------------------------------------
ip_domain_name = "sbod-ece-dev-1-v1-pub-ip"
# --------------------------------------------------------------------------------------------------

admin_group_object_id = "03594057-9fbb-414b-aa9e-f2f089410b46"
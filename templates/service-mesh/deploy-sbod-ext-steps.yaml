parameters:
  azureSubscription: ''
  azureK8sResourceGroup: ''
  kubernetesCluster: ''
  namespace: ''
  chartPath: ''
  configFilePath: ''
  chartReleaseName: ''

steps:
  - checkout: self
    persistCredentials: true

  - task: He<PERSON><PERSON><PERSON>alle<PERSON>@0
    displayName: 'Install Helm v3.12.3'
    inputs:
      helmVersion: v3.12.3
      checkLatestHelmVersion: false

  - task: <PERSON>beloginInstaller@0

  - task: HelmDeploy@0
    displayName: 'Deploy Service Mesh'
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      namespace: "${{ parameters.namespace }}"
      command: upgrade
      chartType: FilePath
      chartPath: "${{ parameters.chartPath }}"
      releaseName: "${{ parameters.chartReleaseName }}"
      valueFile: "${{ parameters.configFilePath }}"
      resetValues: true
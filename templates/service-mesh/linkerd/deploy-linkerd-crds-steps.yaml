parameters:
  azureSubscription: ''
  azureK8sResourceGroup: ''
  kubernetesCluster: ''
  namespace: ''
  chartPath: ''
  configFilePath: ''
  chartReleaseName: ''

steps:
  - checkout: self
    persistCredentials: true

  - task: He<PERSON><PERSON><PERSON>aller@0
    displayName: 'Install Helm v3.12.3'
    inputs:
      helmVersion: v3.12.3
      checkLatestHelmVersion: false

  - task: KubeloginInstaller@0
  
  - task: Kubernetes@1
    displayName: 'Create NS'
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscriptionEndpoint: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      command: apply
      useConfigurationFile: true
      configurationType: inline
      inline: |
        apiVersion: v1
        kind: Namespace
        metadata:
          name: "${{ parameters.namespace }}"

  - task: HelmDeploy@0
    displayName: 'Deploy Service Mesh Linkerd Crds'
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      namespace: "${{ parameters.namespace }}"
      command: upgrade
      chartType: FilePath
      chartPath: "${{ parameters.chartPath }}"
      releaseName: "${{ parameters.chartReleaseName }}"
      valueFile: "${{ parameters.configFilePath }}"
      resetValues: true
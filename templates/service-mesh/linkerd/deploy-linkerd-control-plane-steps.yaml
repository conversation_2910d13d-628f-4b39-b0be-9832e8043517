parameters:
  azureSubscription: ''
  azureK8sResourceGroup: ''
  kubernetesCluster: ''
  namespace: ''
  chartPath: ''
  configFilePath: ''
  chartReleaseName: ''
  akvName: ''

steps:
  - checkout: self
    persistCredentials: true

  - task: <PERSON><PERSON><PERSON><PERSON>alle<PERSON>@0
    displayName: 'Install Helm v3.12.3'
    inputs:
      helmVersion: v3.12.3
      checkLatestHelmVersion: false
  
  - task: KubeloginInstaller@0

  - task: Kubernetes@1
    displayName: 'Create NS'
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscriptionEndpoint: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      command: apply
      useConfigurationFile: true
      configurationType: inline
      inline: |
        apiVersion: v1
        kind: Namespace
        metadata:
          name: "${{ parameters.namespace }}"

  - task: AzureKeyVault@2
    displayName: 'Azure Key Vault'
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      KeyVaultName: "${{ parameters.akvName }}"
      SecretsFilter: >
        linkerd-ca-crt,
        linkerd-issuer-crt,
        linkerd-issuer-key

  - task: Bash@3
    displayName: Decode and save secret as files
    inputs:
      targetType: "inline"
      script: |
        echo -n "$(linkerd-ca-crt)" | base64 -d > /tmp/ca.crt
        echo -n "$(linkerd-issuer-crt)" | base64 -d > /tmp/issuer.crt
        echo -n "$(linkerd-issuer-key)" | base64 -d > /tmp/issuer.key
      failOnExitCode: true

  - task: HelmDeploy@0
    displayName: 'Deploy Service Mesh Linkerd Control Plane'
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      namespace: "${{ parameters.namespace }}"
      command: upgrade
      chartType: FilePath
      chartPath: "${{ parameters.chartPath }}"
      releaseName: "${{ parameters.chartReleaseName }}"
      valueFile: "${{ parameters.configFilePath }}"
      resetValues: true
      arguments: >
        --set-file identityTrustAnchorsPEM=/tmp/ca.crt
        --set-file identity.issuer.tls.crtPEM=/tmp/issuer.crt
        --set-file identity.issuer.tls.keyPEM=/tmp/issuer.key
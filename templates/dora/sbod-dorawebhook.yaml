parameters:
  - name: repositoryUrl
  - name: buildRegion
    default: "ece"
  - name: application

steps:
  - checkout: ${{ parameters.application }}
    persistCredentials: true

  - task: AzureKeyVault@2
    displayName: "Azure KeyVault: Get DORA API token"
    inputs:
      azureSubscription: "ECE-NPRD-NPRWEUB-SBOD"
      KeyVaultName: "sbod-nonprod"
      SecretsFilter: "azure-tenant-id, sp-ece-nprweub-sbod-clientid, sp-ece-nprweub-sbod-clientsecret, dora-sas-token, dora-curl-auth, tec-user-pat"

  - script: |
      response=$(curl -X POST -H 'Content-Type: application/x-www-form-urlencoded' \
      https://login.microsoftonline.com/$(azure-tenant-id)/oauth2/v2.0/token \
      -d 'client_id=$(sp-ece-nprweub-sbod-clientid)/' \
      -d 'grant_type=client_credentials' \
      -d 'scope=499b84ac-1321-427f-aa17-267ca6975798/.default' \
      -d 'client_secret=$(sp-ece-nprweub-sbod-clientsecret)' )
      token=$(echo $response | /usr/bin/jq --raw-output '.access_token')

      echo "Cloning the DORAWEBHOOK Repository"
      git clone https://${token}@dev.azure.com/daimler-mic/Correlation/_git/DoraWebhook -b master
      if [ $? -eq 0 ]; then
        echo "Cloning the repository is successful."
      else
        echo "Cloning DORAWEBHOOK Failed. Verify if the access token is valid or not."
        exit 1
      fi
    displayName: 'Clone DORAWEBHOOK Repository'

  - script: |
      export REPOSITORY_URL=${{ parameters.repositoryUrl }}
      export SOURCE_BRANCH="main"
      export COMMIT_ID=$(Build.SourceVersion)
      export BUILD_REGION=${{ parameters.buildRegion }}
      export sasToken="$(dora-sas-token)"
      export PAT="$(tec-user-pat)"
      export CURL_AUTH="$(dora-curl-auth)"
      export IS_HOTFIX=false

      cd DoraWebhook
      chmod +x dora-script.sh
      ./dora-script.sh
    displayName: 'Execute DORA Scripts'
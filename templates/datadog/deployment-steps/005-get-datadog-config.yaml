parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: resourceSize
  - name: ingressNS

steps:
  - task: AzureCLI@1
    displayName: 'Get Datadog Config'
    condition: succeeded()
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      scriptPath: '$(System.DefaultWorkingDirectory)/scripts/get_datadog_tags.sh'
      arguments: '${{ parameters.cluster_rg }} ${{ parameters.cluster_name }} ${{ parameters.resourceSize }} ${{ parameters.ingressNS }}'
      workingDirectory: '$(System.DefaultWorkingDirectory)/scripts'

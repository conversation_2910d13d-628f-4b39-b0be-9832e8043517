parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: value_file
  - name: namespace
  - name: release_name
  - name: chart_name
  - name: chart_version

steps:
  - task: <PERSON><PERSON><PERSON><PERSON><PERSON>nstaller@0

  - task: He<PERSON>Deploy@0
    displayName: 'Helm deploy datadog'
    condition: succeeded()
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscription: '${{ parameters.subscription }}'
      azureResourceGroup: '${{ parameters.cluster_rg }}'
      kubernetesCluster: '${{ parameters.cluster_name }}'
      namespace: '${{ parameters.namespace }}'
      command: upgrade
      chartType: 'Name'
      releaseName: ${{ parameters.release_name }}
      chartName: ${{ parameters.chart_name }}
      valueFile: ${{ parameters.value_file }}
      overrideValues: |
        datadog.clusterName=$(datadogClustername)
        datadog.tags[0]=env:$(stageTag)
        datadog.tags[1]=region:$(regionTag)
        datadog.tags[2]=geo:$(geoTag)
        datadog.tags[3]=mic_project:$(datadogProject)
        datadog.tags[4]=mic_department:$(datadogDepartment)
        datadog.apiKey=$(tm-datadog-api-key)
        datadog.appKey=$(tm-datadog-app-key)
      install: true
      arguments: '--version ${{ parameters.chart_version }} -f $(System.DefaultWorkingDirectory)/scripts/mod_values.yml'

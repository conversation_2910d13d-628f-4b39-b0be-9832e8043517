parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: namespace
  - name: release_name

steps:
  - task: AzureCLI@1
    displayName: 'Delete previous datadog deployment'
    condition: succeeded()
    inputs:
      azureSubscription: '${{ parameters.subscription }}'
      scriptPath: '$(System.DefaultWorkingDirectory)/scripts/helm_delete_datadog.sh'
      arguments: '${{ parameters.cluster_name }} ${{ parameters.cluster_rg }} ${{ parameters.namespace }} ${{ parameters.release_name }}'
      workingDirectory: '$(System.DefaultWorkingDirectory)/terraform/scripts'
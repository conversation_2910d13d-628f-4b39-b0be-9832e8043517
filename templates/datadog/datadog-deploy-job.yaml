parameters:
  - name: jobName
  - name: jobDisplayName
    default: "Deploy Datadog"
  - name: vmImage
    default: "ubuntu-latest"
  - name: helmVersion
    default: "3.14.0"
  - name: subscription
    default: "ADO-IAMS-DaiVB IAM Services"
  - name: subscriptionVault
    default: "ADO-IAMS-DaiVB IAM Services"

  - name: akvName
  - name: keyVaultSecretsFilter

  - name: aksClusterResourceGroup
  - name: aksClusterName

  - name: datadogResourceSize
  - name: ingressNamespace

  - name: datadogNamespace
  - name: datadogHelmReleaseName
  - name: datadogChartName
  - name: datadogChartVersion
  - name: datadogChartValueFile

  - name: deployProceed
    type: boolean
    default: false

jobs:
  - job: ${{ parameters.jobName }}
    displayName: ${{ parameters.jobDisplayName }}
    pool:
      vmImage: ${{ parameters.vmImage }}
    steps:
      - checkout: self
        persistCredentials: true

      - template: deployment-steps/001-install-helm.yaml
        parameters:
          version: ${{ parameters.helmVersion }}

      - template: deployment-steps/002-install-kubectl.yaml

      - template: deployment-steps/003-add-helm-repo.yaml

      - template: deployment-steps/004-secret-download.yaml
        parameters:
          subscription: ${{ parameters.subscriptionVault }}
          vaultName: ${{ parameters.akvName }}
          secretsFilter: ${{ parameters.keyVaultSecretsFilter }}

      - template: deployment-steps/005-get-datadog-config.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          resourceSize: ${{ parameters.datadogResourceSize }}
          ingressNS: ${{ parameters.ingressNamespace }}

      - template: deployment-steps/006-delete-prev-datadog-deployment.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.datadogNamespace }}
          release_name: ${{ parameters.datadogHelmReleaseName }}

      - template: deployment-steps/007-create-datadog-ns.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.datadogNamespace }}

      - template: deployment-steps/008-deploy-datadog.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.datadogNamespace }}
          release_name: ${{ parameters.datadogHelmReleaseName }}
          chart_name: ${{ parameters.datadogChartName }}
          chart_version: ${{ parameters.datadogChartVersion }}
          value_file: ${{ parameters.datadogChartValueFile }}

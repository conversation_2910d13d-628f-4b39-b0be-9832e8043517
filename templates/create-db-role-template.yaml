parameters:
  - name: serviceConnection
  - name: spVaultName
  - name: applicationVaultName
  - name: vaultSecretKey
  - name: schemaName
  - name: workloadIdentityOid

steps:
  - task: AzureKeyVault@2
    displayName: "Azure Key Vault: Get DB fqdn and admin user"
    inputs:
      ConnectedServiceName: "${{ parameters.serviceConnection }}"
      KeyVaultName: "${{ parameters.spVaultName }}"
      SecretsFilter: "jdbc-fqdn, jdbc-admin-user"

  - task: AzureCLI@1
    displayName: "Create Role Assignment in DB"
    inputs:
      connectedServiceNameARM: "${{ parameters.serviceConnection }}"
      scriptPath: "$(System.DefaultWorkingDirectory)/scripts/create_db_schema.sh"
      arguments: "$(jdbc-fqdn) $(jdbc-admin-user) sbod-db ${{ parameters.schemaName }} ${{ parameters.workloadIdentityOid }}"
      workingDirectory: "$(System.DefaultWorkingDirectory)/scripts/"

  - task: AzureCLI@1
    displayName: "Store Role name into application Key Vault"
    inputs:
      connectedServiceNameARM: "${{ parameters.serviceConnection }}"
      scriptLocation: inlineScript
      inlineScript: |
        KEY=$(echo '${{ parameters.vaultSecretKey }}' | tr _ -)
        az keyvault secret set --name $KEY \
          --value ${{ parameters.schemaName }} \
          --vault-name ${{ parameters.applicationVaultName }}

parameters:
  azureSubscription: ''
  azureK8sResourceGroup: ''
  kubernetesCluster: ''
  namespace: ''
  chartPath: ''
  configFilePath: ''
  chartReleaseName: ''
  akvName: ''

steps:
  - checkout: self
    persistCredentials: true

  - task: He<PERSON><PERSON>nstaller@0
    displayName: 'Install Helm v3.12.3'
    inputs:
      helmVersion: v3.12.3
      checkLatestHelmVersion: false

  - task: Kubernetes@1
    displayName: 'Create NS'
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscriptionEndpoint: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      command: apply
      useConfigurationFile: true
      configurationType: inline
      inline: |
        apiVersion: v1
        kind: Namespace
        metadata:
          name: "${{ parameters.namespace }}"

  - task: AzureKeyVault@2
    displayName: 'Azure Key Vault'
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      KeyVaultName: "${{ parameters.akvName }}"
      SecretsFilter: >
        sbod-argocd-tokenmaster-clientid,
        sbod-argocd-tokenmaster-clientsecret,
        sbod-argocd-dvb-corpinter-net-ssl-cert,
        sbod-argocd-dvb-corpinter-net-ssl-key,
        sbod-argocd-dvb-corpinter-net-ca-cert

  - task: HelmDeploy@0
    displayName: 'Deploy ArgoCD'
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      namespace: "${{ parameters.namespace }}"
      command: upgrade
      chartType: FilePath
      chartPath: "${{ parameters.chartPath }}"
      releaseName: "${{ parameters.chartReleaseName }}"
      valueFile: "${{ parameters.configFilePath }}"
      resetValues: true
      arguments: >
        --set sso.clientID=$(sbod-argocd-tokenmaster-clientid)
        --set sso.clientSecret=$(sbod-argocd-tokenmaster-clientsecret)
        --set tls.crt=$(sbod-argocd-dvb-corpinter-net-ssl-cert)
        --set tls.key=$(sbod-argocd-dvb-corpinter-net-ssl-key)
        --set ca.crt=$(sbod-argocd-dvb-corpinter-net-ca-cert)
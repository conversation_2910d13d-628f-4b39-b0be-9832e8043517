parameters:
  - name: imageTag
  - name: chartReleaseName
  - name: azureSubscription
  - name: azureK8sResourceGroup
  - name: kubernetesCluster
  - name: chartPath
  - name: configFilePath
  - name: repoName
  - name: namespace
  - name: branchName

steps:
  - task: <PERSON><PERSON><PERSON>nstaller@1
    displayName: "Install Helm"
    inputs:
      helmVersionToInstall: v3.12.3

  - task: He<PERSON><PERSON><PERSON>loy@0
    displayName: "Deploy SBOD microservices"
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      namespace: "${{ parameters.namespace }}"
      command: "upgrade"
      chartType: FilePath
      chartPath: "${{ parameters.chartPath }}"
      releaseName: "${{ parameters.chartReleaseName }}"
      chartName: "${{ parameters.chartReleaseName }}"
      valueFile: "${{ parameters.configFilePath }}"
      resetValues: true
      overrideValues: |
        image.tag=${{ parameters.imageTag }}
        branchName=${{ parameters.branchName }}

  - task: Kubernetes@1
    displayName: "Delete old pods"
    inputs:
      connectionType: "Azure Resource Manager"
      azureSubscriptionEndpoint: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      command: delete
      arguments: "-n sbod-dev pods -l app.kubernetes.io/instance=${{ parameters.chartReleaseName }}"

parameters:
  - name: commonReleaseName
  - name: branchName
  - name: repoName
  - name: skipTestCase
  - name: environment

steps:
  - bash: |
      cd $(Build.SourcesDirectory)/sbod-common
      git pull
      if [[ "${{ parameters.environment }}" == "feature-branch" ]]; 
      then
        echo "feature branch"
        if git rev-parse --verify origin/${{ parameters.branchName }} >/dev/null 2>&1
        then
          echo "branch exists"
          git checkout ${{ parameters.branchName }}
        else
          echo "create new branch"
          git config --global user.email "<EMAIL>"
          git config --global user.name "Release pipeline"
          git checkout -b ${{ parameters.branchName }}
          git commit -m "Create new feature branch for SBOD-common correspond to ${{ parameters.repoName }}" -a
          git push --set-upstream origin ${{ parameters.branchName }}
        fi
      else
        if [[ "${{ parameters.environment }}" == "dev" ]];
        then
          git checkout main
        else
          git checkout -b origin/release
          echo ${{ parameters.branchName }}
          git config --global user.email "<EMAIL>"
          git config --global user.name "Release pipeline"
          git merge origin/${{ parameters.branchName }} --allow-unrelated-histories
          git push --set-upstream origin origin/release
        fi
      fi
      echo "HEAD is at $(git rev-parse HEAD)"

      sed -i -e 's/^commonVersion=.*/commonVersion='${{ parameters.commonReleaseName }}-SNAPSHOT'/' gradle.properties
      echo "Current commonVersion: $(grep ^commonVersion= gradle.properties | cut -d'=' -f2)"

      sed -i -e 's/^sbodUsername=.*/sbodUsername='$(sbod-artifacts-username)'/' gradle.properties
      sed -i -e 's/^sbodPassword=.*/sbodPassword='$(sbod-artifacts-password)'/' gradle.properties
    displayName: "Switch SBOD common to targeted branch"

  - task: Gradle@3
    displayName: "Execute test case for sbod-common"
    condition: and(succeeded(), eq(${{parameters.skipTestCase}}, false))
    inputs:
      workingDirectory: "$(Build.SourcesDirectory)/sbod-common"
      gradleWrapperFile: "$(Build.SourcesDirectory)/sbod-common/gradlew"
      gradleOptions: "-Xmx3072m"
      options: "--stacktrace"
      javaHomeOption: "JDKVersion"
      jdkVersionOption: "1.17"
      jdkArchitectureOption: "x64"
      tasks: "test"

  - task: Gradle@3
    displayName: Publish sbod common jar file to artifacts
    inputs:
      workingDirectory: "$(Build.SourcesDirectory)/sbod-common"
      gradleWrapperFile: "$(Build.SourcesDirectory)/sbod-common/gradlew"
      gradleOptions: "-Xmx3072m"
      options: "--stacktrace"
      javaHomeOption: "JDKVersion"
      jdkVersionOption: "1.17"
      jdkArchitectureOption: "x64"
      tasks: "publishMavenPublicationToSbodRepository"

steps:
  - checkout: sbod-common
    persistCredentials: true

  - checkout: sbod-core
    persistCredentials: true

  - checkout: sbod-ext
    persistCredentials: true

  - checkout: self
    persistCredentials: true

  - task: KubectlInstaller@0
    displayName: Install kubectl
    inputs:
      kubectlVersion: latest

  - task: JavaToolInstaller@0
    displayName: Install Java
    inputs:
      versionSpec: "17"
      jdkArchitectureOption: "x64"
      jdkSourceOption: "PreInstalled"

  - task: AzureKeyVault@2
    displayName: "Azure KeyVault: Get SecHub API token"
    inputs:
      azureSubscription: "ECE-NPRD-NPRWEUB-SBOD"
      KeyVaultName: "sbod-keepass"
      SecretsFilter: "azure-tenant-id, azure-client-id, azure-client-secret, sbod-prod-tokenmaster-clientid, sbod-prod-tokenmaster-clientsecret"

  - script: |
      sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d -b /usr/local/bin
      cd $(Build.SourcesDirectory)/sbod-core/taskfile
      task container:setupAll
      task container:restartContainer
      task db:createDatabase
      cd $(Build.SourcesDirectory)/sbod-ext/taskfile
      task db:createDatabase
    displayName: "Setup SBOD core and ext"

  - task: Gradle@3
    displayName: "Build SBOD core openAPI specifications "
    inputs:
      workingDirectory: "$(Build.SourcesDirectory)/sbod-core"
      gradleWrapperFile: "$(Build.SourcesDirectory)/sbod-core/gradlew"
      gradleOptions: "-Xmx3072m"
      options: "--stacktrace"
      javaHomeOption: "JDKVersion"
      jdkVersionOption: "1.17"
      jdkArchitectureOption: "x64"
      tasks: "generateOpenApiDocs"
    env:
      AZURE_TENANT_ID: $(azure-tenant-id)
      AZURE_CLIENT_SECRET: $(azure-client-secret)
      AZURE_CLIENT_ID: $(azure-client-id)

  - script: |
      cd $(Build.SourcesDirectory)/sbod-ext/taskfile
      task db:createDigitalKeyView

  - task: Gradle@3
    displayName: "Build SBOD ext openAPI specifications "
    inputs:
      workingDirectory: "$(Build.SourcesDirectory)/sbod-ext"
      gradleWrapperFile: "$(Build.SourcesDirectory)/sbod-ext/gradlew"
      gradleOptions: "-Xmx3072m"
      options: "--stacktrace"
      javaHomeOption: "JDKVersion"
      jdkVersionOption: "1.17"
      jdkArchitectureOption: "x64"
      tasks: "generateOpenApiDocs"
    env:
      AZURE_TENANT_ID: $(azure-tenant-id)
      AZURE_CLIENT_SECRET: $(azure-client-secret)
      AZURE_CLIENT_ID: $(azure-client-id)

  - script: |
      cp $(Build.SourcesDirectory)/sbod-core/build/docs/sbod-api.yaml $(Build.SourcesDirectory)/sbod-infrastructure/configs/mbos_portal/sbod-core.yaml
      cp $(Build.SourcesDirectory)/sbod-ext/build/docs/sbod-api.yaml $(Build.SourcesDirectory)/sbod-infrastructure/configs/mbos_portal/sbod-ext.yaml

      cd $(Build.SourcesDirectory)/sbod-infrastructure/configs/mbos_portal
      ls
    displayName: "Move openAPI specifications to openapi folder"

  - task: UseNode@1
    inputs:
      version: '20.17.0'

  - script: |
      cd $(Build.SourcesDirectory)/sbod-infrastructure/configs/mbos_portal
      npm i openapi-merge-cli
      npx openapi-merge-cli --config $(Build.SourcesDirectory)/sbod-infrastructure/configs/mbos_portal/openapi-merge.json
      cp $(Build.SourcesDirectory)/sbod-infrastructure/configs/mbos_portal/sbod.json $(Build.SourcesDirectory)/sbod-infrastructure/scripts/sbod.json
      
      jq '.info += {
               "title": "Server Based Owner Device (SBOD)",
               "x-nameSlug": "sbod",
               "description": "Server Based Owner Device (SBOD) is the future of digital key",
               "version": "v0.1",
               "x-state": "IN_DEVELOPMENT",
               "x-visibility": "INTERNAL",
               "x-applicationId": "2b5927d7-4ee9-4ad5-85f0-e65ce38ac6a0",
               "x-actions": {
                 "tryItOut": {
                   "enabled": true
                 }
               },
               "x-processors": {
                 "scopeGeneration": {
                   "enabled": true
                 }
               }
             }' sbod.json > tmp.json && mv tmp.json sbod.json
      
      jq 'del (.servers) + {
      "servers": [
        {
          "url": "http://localhost:xxxx/core",
          "description": "Generated server url"
        },
        {
      
          "url": "https://sbod-nonprod.query.api.dvb.corpinter.net",
          "description": "SBOD NONPROD server"
        }
      ]
      }' sbod.json > tmp.json && mv tmp.json sbod.json

    displayName: "Merge openAPI specifications"

  #  - task: ShellScript@2
  #    inputs:
  #      filename: $(Build.SourcesDirectory)/sbod-infrastructure/scripts/update_openapi_spec.sh

  - task: CmdLine@2
    displayName: Get the tokenmaster token scope
    inputs:
      script: |
        response=$(curl --request POST \
        --url https://ssoalpha.dvb.corpinter.net/v1/token \
        -u $(sbod-prod-tokenmaster-clientid):$(sbod-prod-tokenmaster-clientsecret) \
        --header 'Content-Type: application/x-www-form-urlencoded' \
        --data 'scope=openid profile email mic:env:prod groups audience:server:client_id:DAIVBADM_MICTM_EMEA_PROD_00648' \
        --data 'grant_type=client_credentials' )
        token=$(echo $response | /usr/bin/jq --raw-output '.access_token')
        echo $response
        echo $token
        echo "##vso[task.setvariable variable=mbosJwtToken;]$token"

  - task: CmdLine@2
    displayName: Update MBOS portal openAPI specifications
    inputs:
      script: |
        cd $(Build.SourcesDirectory)/sbod-infrastructure/configs/mbos_portal
        echo $(mbosJwtToken)
        curl --request POST \
        --url https://dre.query.api.dvb.corpinter.net/organizations/sbod/register \
        --header 'Authorization: Bearer $(mbosJwtToken)' \
        --header 'Content-Type: application/json' \
        --data '@sbod.json'


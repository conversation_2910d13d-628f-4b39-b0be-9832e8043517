parameters:
  - name: commonReleaseName
  - name: imageTag
  - name: branchName
  - name: repoName
  - name: skipTestCase
  - name: environment
  - name: acrName

steps:
  - bash: |
      cd $(Build.SourcesDirectory)/${{ parameters.repoName }}
      git pull
      if [[ "${{ parameters.environment }}" == "feature-branch" ]];
      then
        git checkout ${{ parameters.branchName }}
      else
        if [[ "${{ parameters.environment }}" == "dev" ]];
        then
          git checkout main
        else
          git checkout -b origin/release
          git config --global user.email "<EMAIL>"
          git config --global user.name "Release pipeline"
          git merge origin/${{ parameters.branchName }} --allow-unrelated-histories
          git push --set-upstream origin origin/release
        fi
      fi
      echo "HEAD is at $(git rev-parse HEAD)"

      sed -i -e 's/^sbodUsername=.*/sbodUsername='$(sbod-artifacts-username)'/' gradle.properties
      sed -i -e 's/^sbodPassword=.*/sbodPassword='$(sbod-artifacts-password)'/' gradle.properties

      sed -i -e 's/^sbodCommonVersion=.*/sbodCommonVersion='${{ parameters.commonReleaseName }}-SNAPSHOT'/' gradle.properties
      sed -i -e 's/sbodCommonVersion\s*=.*/sbodCommonVersion = "'${{ parameters.commonReleaseName }}-SNAPSHOT'"/' dependencies.gradle

      echo "Current commonVersion: $(grep ^sbodCommonVersion= gradle.properties | cut -d'=' -f2)"
    displayName: "Switch SBOD service to targeted branch"

  - task: Gradle@3
    displayName: "Execute test case for sbod service"
    condition: and(succeeded(), eq(${{parameters.skipTestCase}}, false))
    inputs:
      workingDirectory: "$(Build.SourcesDirectory)/${{ parameters.repoName }}"
      gradleWrapperFile: "$(Build.SourcesDirectory)/${{ parameters.repoName }}/gradlew"
      gradleOptions: "-Xmx3072m"
      options: "--stacktrace"
      javaHomeOption: "JDKVersion"
      jdkVersionOption: "1.17"
      jdkArchitectureOption: "x64"
      tasks: "test"
    env:
      AZURE_CLIENT_ID: $(sp-ece-nprweub-sbod-clientid)
      AZURE_CLIENT_SECRET: $(sp-ece-nprweub-sbod-clientsecret)
      AZURE_TENANT_ID: $(sp-workload-test-tenantid)

  - task: Docker@2
    displayName: Build SBOD image
    inputs:
      command: buildAndPush
      repository: ${{ parameters.repoName }}
      containerRegistry: ${{ parameters.acrName }}
      dockerfile: "$(Build.SourcesDirectory)/${{ parameters.repoName }}/Dockerfile"
      tags: ${{ parameters.imageTag }}
      arguments: "--no-cache"

parameters:
  - name: repoName
  - name: branchName
  - name: azureSubscription
  - name: keyvaultName

steps:
  - checkout: sbod-common
    persistCredentials: true
  - checkout: ${{ parameters.repoName }}
    persistCredentials: true
  - checkout: self

  - bash: |
      cd $(Build.SourcesDirectory)/${{ parameters.repoName }}
      echo "branch name ${{ parameters.branchName }}"
      git pull
      if git rev-parse --verify origin/${{ parameters.branchName }} >/dev/null 2>&1
      then
        echo "Branch exists"
      else
        echo "Branch does not exist. Please ensure the branch name is correct and try again."
        exit 1
      fi
    displayName: 'Verify branch name existence'

  - task: KubectlInstaller@0
    displayName: Install kubectl
    inputs:
      kubectlVersion: latest

  - task: KubeloginInstaller@0

  - task: JavaToolInstaller@0
    displayName: Install Java
    inputs:
      versionSpec: "17"
      jdkArchitectureOption: "x64"
      jdkSourceOption: "PreInstalled"

  - task: AzureKeyVault@2
    displayName: "Key Vault: Get secrets"
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      KeyVaultName: "${{ parameters.keyvaultName }}"
      SecretsFilter: "sbod-artifacts-password, sbod-artifacts-username, sp-ece-nprweub-sbod-clientid, sp-ece-nprweub-sbod-clientsecret, sp-workload-test-tenantid"

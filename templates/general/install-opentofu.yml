steps:
  - bash: |
      # Download the installer script:
      curl --proto '=https' --tlsv1.2 -fsSL https://get.opentofu.org/install-opentofu.sh -o install-opentofu.sh
      # Alternatively: wget --secure-protocol=TLSv1_2 --https-only https://get.opentofu.org/install-opentofu.sh -O install-opentofu.sh

      # Give it execution permissions:
      chmod +x install-opentofu.sh

      # Please inspect the downloaded script

      # Run the installer:
      ./install-opentofu.sh --install-method standalone

      # Remove the installer:
      rm -f install-opentofu.sh
    displayName: 'Install OpenTofu'
        

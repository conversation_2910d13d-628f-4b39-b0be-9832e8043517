parameters:
  - name: subscription
  - name: vault_name
  - name: sp_client_id

steps:
  - task: AzureCLI@1
    displayName: "[Copy Me] Grant Permision to Key Vault"
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      scriptLocation: inlineScript
      inlineScript: |
        echo "Grant Permision to Key Vault"
        echo "Copy the code and run it in the terminal"
        echo "========================================"
        echo az keyvault set-policy --name ${{ parameters.vault_name}} \
              --secret-permissions get \
              --spn ${{ parameters.sp_client_id }}

parameters:
  - name: subscription
  - name: service_account_namespace
  - name: service_account_name
  - name: sp_object_id

steps:
  - task: CmdLine@2
    displayName: "[Copy Me] Create federated Credentials"
    inputs:
      script: |
        echo "Copy the code below."
        echo "Please find the owner of service principles to create federated credentials."
        echo "========================================"
        echo 'cat <<EOF > params.json
        {
          "name": "kubernetes-federated-credential",
          "issuer": "$(serviceAccountIssuer)",
          "subject": "system:serviceaccount:${{ parameters.service_account_namespace }}:${{ parameters.service_account_name }}",
          "description": "Kubernetes service account federated credential",
          "audiences": [
            "api://AzureADTokenExchange"
          ]
        }
        EOF'
        echo az ad app federated-credential create --id ${{ parameters.sp_object_id }} --parameters @params.json

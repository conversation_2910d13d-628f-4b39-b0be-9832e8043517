parameters:
  - name: subscription
  - name: aks_cluster
  - name: resource_group
  - name: workload_namespace

steps:
  - task: HelmDeploy@0
    displayName: Upgrade workload-identity-webhook
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      azureResourceGroup: ${{ parameters.resource_group }}
      kubernetesCluster: ${{ parameters.aks_cluster }}
      namespace: ${{ parameters.workload_namespace }}
      command: upgrade
      releaseName: workload-identity-webhook
      chartName: azure-workload-identity/workload-identity-webhook
      resetValues: true
      arguments: "--set azureTenantID=$(azureTenantId)"

parameters:
  - name: subscription
  - name: aks_cluster
  - name: resource_group
  - name: workload_namespace

steps:
  - task: <PERSON><PERSON><PERSON><PERSON>Installe<PERSON>@0
  
  - task: Kubernetes@1
    displayName: "Create Namespace ${{ parameters.workload_namespace  }}"
    inputs:
      connectionType: "Azure Resource Manager"
      azureSubscriptionEndpoint: ${{ parameters.subscription }}
      azureResourceGroup: ${{ parameters.resource_group }}
      kubernetesCluster: ${{ parameters.aks_cluster }}
      command: apply
      useConfigurationFile: true
      configurationType: inline
      inline: |
        apiVersion: v1
        kind: Namespace
        metadata:
          name: ${{ parameters.workload_namespace  }}

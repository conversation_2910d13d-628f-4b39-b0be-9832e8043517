parameters:
  - name: subscription
  - name: subscriptionId
  - name: resource_group
  - name: aks_cluster

steps:
  - task: AzureCLI@1
    displayName: "Get necessary env variables"
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      scriptLocation: inlineScript
      inlineScript: |
        # echo "Enable any OIDC-specific feature flags"
        # az aks update \
        #   --resource-group "${{ parameters.resource_group }}" \
        #   --name "${{ parameters.aks_cluster }}" \
        #   --enable-oidc-issuer 
        # echo "========================================"

        echo "Obtain the OIDC issuer URL when setting up the federated identity credentials"
        SERVICE_ACCOUNT_ISSUER=$(az aks show -n ${{ parameters.aks_cluster }} -g ${{ parameters.resource_group }} --query "oidcIssuerProfile.issuerUrl" -otsv)
        echo "##vso[task.setvariable variable=serviceAccountIssuer;]$SERVICE_ACCOUNT_ISSUER"
        echo "========================================"

        echo "Obtain Azure Tenant Id"
        AZURE_TENANT_ID="$(az account show -s ${{ parameters.subscriptionId }} --query tenantId -otsv)"
        echo "##vso[task.setvariable variable=azureTenantId;]$AZURE_TENANT_ID"
        echo "========================================"

parameters:
  - name: sp_client_id
  - name: service_account_name
  - name: service_account_namespace
  - name: subscription
  - name: aks_cluster
  - name: resource_group

steps:
  - task: Kubernetes@1
    displayName: "Create Service Account ${{ parameters.service_account_name  }}"
    inputs:
      connectionType: "Azure Resource Manager"
      namespace: "${{ parameters.service_account_namespace }}"
      azureSubscriptionEndpoint: ${{ parameters.subscription }}
      kubernetesCluster: ${{ parameters.aks_cluster }}
      azureResourceGroup: ${{ parameters.resource_group }}
      useConfigurationFile: true
      configurationType: inline
      inline: |
        apiVersion: v1
        kind: ServiceAccount
        metadata:
          annotations:
            azure.workload.identity/client-id: ${{ parameters.sp_client_id }}
          labels:
            azure.workload.identity/use: "true"  
          name: ${{ parameters.service_account_name}}
          namespace: ${{ parameters.service_account_namespace }}
      command: "apply"

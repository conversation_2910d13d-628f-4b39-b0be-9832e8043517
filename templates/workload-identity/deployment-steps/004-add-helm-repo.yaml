parameters:
  - name: subscription
  - name: aks_cluster
  - name: resource_group
  - name: workload_namespace

steps:
  - task: AzureCLI@1
    displayName: "Helm Install Workload Identity"
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      scriptLocation: inlineScript
      inlineScript: |
        helm repo add azure-workload-identity https://azure.github.io/azure-workload-identity/charts
        helm repo update

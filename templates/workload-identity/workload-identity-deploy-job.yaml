parameters:
  - name: jobName
  - name: jobDisplayName
    default: "Deploy Workload Identity"
  - name: vmImage
    default: "ubuntu-latest"
  - name: helmVersion
    default: "3.14.0"

  - name: aksClusterResourceGroup
  - name: aksClusterName
  - name: subscription
  - name: subscriptionId

  - name: workloadNamespace
    default: "azure-workload-identity-system-dev01"

  - name: akvName

  - name: servicePrincipleClientId
  - name: servicePrincipleObjectId

  - name: serviceAccountNS
  - name: serviceAccountName

jobs:
  - job: ${{ parameters.jobName }}
    displayName: ${{ parameters.jobDisplayName }}
    pool:
      vmImage: ${{ parameters.vmImage }}
    steps:
      - checkout: self
        persistCredentials: true

      - template: deployment-steps/001-install-helm.yaml
        parameters:
          version: ${{ parameters.helmVersion }}

      - template: deployment-steps/002-install-kubectl.yaml

      - template: deployment-steps/003-get-env-variables.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          subscriptionId: ${{ parameters.subscriptionId }}
          resource_group: ${{ parameters.aksClusterResourceGroup }}
          aks_cluster: ${{ parameters.aksClusterName }}

      - template: deployment-steps/004-add-helm-repo.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          aks_cluster: ${{ parameters.aksClusterName }}
          resource_group: ${{ parameters.aksClusterResourceGroup }}
          workload_namespace: ${{ parameters.workloadNamespace }}

      - template: deployment-steps/005-create-namespace.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          aks_cluster: ${{ parameters.aksClusterName }}
          resource_group: ${{ parameters.aksClusterResourceGroup }}
          workload_namespace: ${{ parameters.workloadNamespace }}

      - template: deployment-steps/006-helm-install.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          aks_cluster: ${{ parameters.aksClusterName }}
          resource_group: ${{ parameters.aksClusterResourceGroup }}
          workload_namespace: ${{ parameters.workloadNamespace }}

      - template: deployment-steps/007-create-service-account.yaml
        parameters:
          service_account_namespace: ${{ parameters.serviceAccountNS }}
          service_account_name: ${{ parameters.serviceAccountName }}
          sp_client_id: ${{ parameters.servicePrincipleClientId }}
          subscription: ${{ parameters.subscription }}
          resource_group: ${{ parameters.aksClusterResourceGroup }}
          aks_cluster: ${{ parameters.aksClusterName }}

      - template: deployment-steps/008-grant-secret-permission.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          vault_name: ${{ parameters.akvName }}
          sp_client_id: ${{ parameters.servicePrincipleClientId }}

      - template: deployment-steps/009-establish-federal-identity.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          service_account_namespace: ${{ parameters.serviceAccountNS }}
          service_account_name: ${{ parameters.serviceAccountName }}
          sp_object_id: ${{ parameters.servicePrincipleObjectId }}

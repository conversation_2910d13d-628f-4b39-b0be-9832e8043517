parameters:
- name: BLACK_DUCK_API_TOKEN
- name: BLACK_DUCK_URL
- name: BLACK_DUCK_PROJECT_NAME
- name: BLACK_DUCK_PROJECT_VERSION
- name: DISCO_API_TOKEN
- name: DISCO_HOST
- name: DISCO_PROJECT_UUID
- name: DISCO_PROJECT_VERSION
- name: DISCO_SBOM_TAG
- name: BLACK_DUCK_MB_TEMPLATE

steps:
  - script: |
      echo "Authorization"
      tokenResponse=$(curl -s -S -X POST "${{ parameters.BLACK_DUCK_URL }}/api/tokens/authenticate" \
                      -H "Authorization: token ${{ parameters.BLACK_DUCK_API_TOKEN }}" \
                      -H "Accept: application/vnd.blackducksoftware.user-4+json")
      bearerToken=$(echo "$tokenResponse" | jq -r '.bearerToken')
      
      echo "Lookup Project"
      encoded_project_name=$(jq -rn --arg name "${{ parameters.BLACK_DUCK_PROJECT_NAME }}" '$name|@uri')
      projectResponse=$(curl -s -S -X GET "${{ parameters.BLACK_DUCK_URL }}/api/projects?q=name:$encoded_project_name" \
                      -H "Authorization: Bearer $bearerToken" \
                      -H "Accept: application/json" \
                      -H "Content-Type: application/vnd.blackducksoftware.report-4+json")
      totalCount=$(echo "$projectResponse" | jq '.totalCount')
      projectUrl=""
      if [ "$totalCount" -gt 0 ]; then
        projectUrl=$(echo "$projectResponse" | jq -r --arg PNAME "${{ parameters.BLACK_DUCK_PROJECT_NAME }}" '.items[] | select(.name==$PNAME)._meta.href' | head -n 1)
        if [ -z "$projectUrl" ]; then
          echo "No matching item found for requested project name."
          exit 1
        fi
      else
        echo "Project lookup returns 0 items."
        exit 1
      fi
      
      echo "Lookup Version"
      encoded_version_name=$(jq -rn --arg name "${{ parameters.BLACK_DUCK_PROJECT_VERSION }}" '$name|@uri')
      versionResponse=$(curl -s -S -X GET "$projectUrl/versions?q=name:$encoded_version_name" \
                      -H "Authorization: Bearer $bearerToken" \
                      -H "Accept: application/json" \
                      -H "Content-Type: application/vnd.blackducksoftware.report-4+json")
      totalCountVersion=$(echo "$versionResponse" | jq '.totalCount')
      versionUrl=""
      if [ "$totalCountVersion" -gt 0 ]; then
        versionUrl=$(echo "$versionResponse" | jq -r --arg VNAME "${{ parameters.BLACK_DUCK_PROJECT_VERSION }}" '.items[] | select(.versionName==$VNAME)._meta.href' | head -n 1)
        if [ -z "$versionUrl" ]; then
          echo "No matching item found for requested version name."
          exit 1
        fi
      else
        echo "Version lookup returns 0 items."
        exit 1
      fi
      
      echo "Create Report (JSON, SPDX 2.3)"
      if [ -z "${{ parameters.BLACK_DUCK_MB_TEMPLATE }}" ]; then
        createReportPayload='{"reportFormat": "JSON", "sbomType": "SPDX_23"}'
      else
        createReportPayload='{"reportFormat": "JSON", "sbomType": "SPDX_23", "template": "https://bdscan.i.mercedes-benz.com/api/sbom-templates/d91c6761-cbb4-4594-8fb2-90adfa63b8f9"}'
      fi
      createResponse=$(curl -s -S -i -X POST "$versionUrl/sbom-reports" \
                      -H "Authorization: Bearer $bearerToken" \
                      -H "Accept: application/json" \
                      -H "Content-Type: application/vnd.blackducksoftware.report-4+json" \
                      -d "$createReportPayload")
      
      # Check if the response is okay (200 or 201)
      http_status=$(echo "$createResponse" | grep HTTP/ | tail -1 | awk '{print $2}')
      echo "HTTP Status: $http_status"
      
      location_url=""
      if [ "$http_status" -ne 200 ] && [ "$http_status" -ne 201 ]; then
        echo "Failed to create Report, HTTP status: $http_status"
        exit 1
      fi
      
      location_url=$(echo "$createResponse" | grep location | tail -1 | awk '{print $2}' | tr -d '\r')
      if [ -z "$location_url" ]; then
        echo "Unable to resolve Report location url from create report response"
      fi
      echo "Got Report Location URL"
      
      echo "Wait for Report ready..."
      status=""
      while [ "$status" != "COMPLETED" ]; do
        response=$(curl -s -S -X GET "$location_url" \
                    -H "Authorization: Bearer $bearerToken" \
                    -H "Accept: application/json" \
                    -H "Content-Type: application/vnd.blackducksoftware.report-4+json")
        status=$(echo "$response" | jq -r '.status')
        echo "Current status: $status"
        if [ "$status" == "FAILED" ]; then
          echo "Report creation failed with status: $status"
          exit 1
        fi
        if [ "$status" != "COMPLETED" ]; then
          sleep 5
        fi
      done
      
      echo "Download Report"
      download_url="${location_url}/download"
      curl -s -S -X GET "$download_url" \
        -H "Authorization: Bearer $bearerToken" \
        -H "Accept: application/json" \
        -H "Content-Type: application/vnd.blackducksoftware.report-4+json" \
        -o "report.zip"
      
      mkdir extracted
      unzip report.zip -d extracted
      json_file=$(find extracted -type f -name 'sbom_*.spdx.json')
      echo "Copy found JSON to ./sbom.json"
      cp "$json_file" ./sbom.json
        
      echo "Upload JSON SBOM to Disclosure Portal"
      DISCO_PROJECT_VERSION_ENC=$(jq -rn --arg name "${{ parameters.DISCO_PROJECT_VERSION }}" '$name|@uri')
      echo "${{ parameters.DISCO_HOST }}/projects/${{ parameters.DISCO_PROJECT_UUID }}/versions/$DISCO_PROJECT_VERSION_ENC/sboms"
      jsonResult=$(curl -v -s -S -X POST "${{ parameters.DISCO_HOST }}/projects/${{ parameters.DISCO_PROJECT_UUID }}/versions/$DISCO_PROJECT_VERSION_ENC/sboms" \
                    -H "Authorization:DISCO ${{ parameters.DISCO_API_TOKEN }}" \
                    -F "file=@sbom.json;filename=sbom.json" \
                    -F "sbomTag=${{ parameters.DISCO_SBOM_TAG }}" \
                    --connect-timeout 300 \
                    --max-time 300 | jq '.')
      
      resultSuccess=$(echo "$jsonResult" | jq -c '. | select (.docIsValid )' | jq -c '.docIsValid')
      if [ "$resultSuccess" = "true" ]
      then
        echo "Upload SUCCESS"
      else
        echo "FAILURE with response '$jsonResult'"
        exit 1
      fi
    displayName: 'BD to Disco'

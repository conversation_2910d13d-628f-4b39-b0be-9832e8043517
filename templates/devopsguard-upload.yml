parameters:
  - name: productName
  - name: engagementName
  - name: api<PERSON>ey
  - name: authentication
  - name: testType # "codescan", "iac", "webscan", "secretscan"
    default: codescan
  - name: reportPath
    default: "$(find -name 'sechub_report*.json')"

steps:
  - script: |
      echo Uploading SecHub scan report to DevOpsGuard...
      curl --fail-with-body --location 'https://devopsguard.i.mercedes-benz.com:8888/upload-scan/' \
        --user "${{ parameters.authentication }}" \
        --form "PRODUCT_NAME=${{ parameters.productname }}" \
        --form "ENGAGEMENT_NAME=${{ parameters.engagementName }}" \
        --form "TEST_TYPE=${{ parameters.testType }}" \
        --form "APIKEY=${{ parameters.apiKey }}" \
        --form "SechubReport.json=@${{ parameters.reportPath }}"
    displayName: "Upload SecHub report to DevOpsG<PERSON>"

parameters:
  - name: envName
  - name: regName
  - name: VarFolder
  - name: VaultName
  - name: VaultSecret1
  - name: VaultSecret2
  - name: AzureSubs
  - name: AzureSubscriptionId
  - name: AzureTenantId
  - name: ResourceGroupName
  - name: ResourceGroupLocation
  - name: StorageAccountName
  - name: <PERSON>orageContainerName
  - name: TfstateBlobName
  - name: varDir
  - name: Preview

steps:
  - task: AzureKeyVault@2
    displayName: "Azure Key Vault - Get secrets"
    inputs:
      azureSubscription: "${{ parameters.AzureSubs }}"
      KeyVaultName: "${{ parameters.VaultName }}"
      SecretsFilter: "${{ parameters.VaultSecret1 }},${{ parameters.VaultSecret2 }}"

  - template: general/install-opentofu.yml

  - task: AzureCLI@1
    displayName: "Create azure credentials"
    inputs:
      azureSubscription: "${{ parameters.AzureSubs }}"
      scriptPath: "$(System.DefaultWorkingDirectory)/scripts/create_auto.tfvars.sh"
      arguments: "client_id $(${{ parameters.VaultSecret1 }}) client_secret $(${{ parameters.VaultSecret2 }})"
      workingDirectory: "$(System.DefaultWorkingDirectory)/terraform/aks-cluster"

  - task: CopyFiles@2
    displayName: "Copy var file to source dir"
    condition: succeeded()
    inputs:
      SourceFolder: "$(System.DefaultWorkingDirectory)/${{ parameters.varDir }}${{ parameters.envName }}/${{ parameters.VarFolder }}"
      Contents: "*.tfvars"
      TargetFolder: "$(System.DefaultWorkingDirectory)/terraform/aks-cluster"
      OverWrite: true

  - task: AzureCLI@1
    displayName: "Create storage account"
    condition: succeeded()
    inputs:
      azureSubscription: "${{ parameters.AzureSubs }}"
      scriptPath: "$(System.DefaultWorkingDirectory)/scripts/storage_account.sh"
      arguments: "${{ parameters.StorageAccountName }} ${{ parameters.ResourceGroupName }} ${{ parameters.ResourceGroupLocation }} ${{ parameters.StorageContainerName }}"
      workingDirectory: "$(System.DefaultWorkingDirectory)/scripts/"

  - bash: |
      cd '$(System.DefaultWorkingDirectory)/terraform/aks-cluster'
      if [ "${{ parameters.regName }}" == "china" ]; then 
        sed -ie 's/public/china/g' main.tf
        echo "Deploying to Azure China cloud!!!"
      else
        echo "Deploying to Azure Public cloud!!!"  
      fi
    displayName: "Set cloud region"

  - bash: |
      tofu init \
       -backend-config="resource_group_name=${{ parameters.ResourceGroupName }}" \
       -backend-config="storage_account_name=${{ parameters.StorageAccountName }}" \
       -backend-config="container_name=${{ parameters.StorageContainerName }}" \
       -backend-config="key=${{ parameters.TfstateBlobName }}"
    displayName: "OpenTofu Init"
    workingDirectory: "$(System.DefaultWorkingDirectory)/terraform/aks-cluster"
    env:
      ARM_SUBSCRIPTION_ID: ${{ parameters.AzureSubscriptionId }}
      ARM_TENANT_ID: ${{ parameters.AzureTenantId }}
      ARM_CLIENT_ID: $(${{ parameters.VaultSecret1 }})
      ARM_CLIENT_SECRET: $(${{ parameters.VaultSecret2 }})

  - bash: |
      tofu plan -out out.plan -lock=true
    displayName: "OpenTofu Plan"
    workingDirectory: "$(System.DefaultWorkingDirectory)/terraform/aks-cluster"
    env:
      ARM_SUBSCRIPTION_ID: ${{ parameters.AzureSubscriptionId }}
      ARM_TENANT_ID: ${{ parameters.AzureTenantId }}
      ARM_CLIENT_ID: $(${{ parameters.VaultSecret1 }})
      ARM_CLIENT_SECRET: $(${{ parameters.VaultSecret2 }})

  - bash: |
      tofu apply -lock=true out.plan
    displayName: "OpenTofu Apply"
    condition: and(succeeded(), eq('${{ parameters.Preview }}', false))
    workingDirectory: "$(System.DefaultWorkingDirectory)/terraform/aks-cluster"
    env:
      ARM_SUBSCRIPTION_ID: ${{ parameters.AzureSubscriptionId }}
      ARM_TENANT_ID: ${{ parameters.AzureTenantId }}
      ARM_CLIENT_ID: $(${{ parameters.VaultSecret1 }})
      ARM_CLIENT_SECRET: $(${{ parameters.VaultSecret2 }})

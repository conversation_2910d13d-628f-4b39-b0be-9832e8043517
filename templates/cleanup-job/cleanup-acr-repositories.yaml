parameters:
  - name: branchName
  - name: azureSubscription
  - name: acrName

steps:
  - task: AzureCLI@2
    displayName: Azure CLI
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        az account show
        az acr repository delete -n ${{ parameters.acrName }} --image sbod-core:${{ parameters.branchName }} -y
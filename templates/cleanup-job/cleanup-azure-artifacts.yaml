parameters:
  - name: branchName
  - name: keyvaultName
  - name: azureSubscription

steps:
  - task: AzureKeyVault@2
    displayName: "Azure Key Vault: Get Artifacts credentials"
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      KeyVaultName: "${{ parameters.keyvaultName }}"
      SecretsFilter: "sbod-artifacts-password, sbod-artifacts-username"

  - task: AzureCLI@1
    displayName: "Delete common jar from artifacts"
    condition: succeeded()
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      scriptPath: '$(System.DefaultWorkingDirectory)/scripts/cleanup_artifacts.sh'
      arguments: '$(sbod-artifacts-username) $(sbod-artifacts-password) ${{ parameters.branchName }}'
      workingDirectory: '$(System.DefaultWorkingDirectory)/scripts'


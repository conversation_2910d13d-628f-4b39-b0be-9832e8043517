parameters:
  - name: branchName
  - name: azureSubscription
  - name: azureK8sResourceGroup
  - name: kubernetesCluster
  - name: keyvaultName

steps:
  - task: <PERSON>bectlInstaller@0
    displayName: Kubectl installer
    inputs:
      kubectlVersion: latest

  - task: <PERSON><PERSON><PERSON><PERSON>Installer@0

  - task: <PERSON>bernetes@1
    displayName: "kubectl delete feature branch deployments"
    inputs:
      connectionType: "Azure Resource Manager"
      azureSubscriptionEndpoint: "${{ parameters.azureSubscription }}"
      azureResourceGroup: "${{ parameters.azureK8sResourceGroup }}"
      kubernetesCluster: "${{ parameters.kubernetesCluster }}"
      command: delete
      arguments: "-n sbod-dev deployment sbod-dev-${{ parameters.branchName }}-sbod-core"
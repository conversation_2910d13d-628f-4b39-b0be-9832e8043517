parameters:
  - name: configPath
  - name: downloadReport
  - name: reportFilePath
  - name: sechubServer
  - name: sechubUserId
  - name: sechubApiToken
  - name: continueOnError

steps:
  - task: Bash@3
    continueOnError: ${{ parameters.continueOnError }}
    displayName: Execute SecHub
    inputs:
      targetType: inline
      script:
        sechub scan -configfile ${{ parameters.configPath }}
    env:
      SECHUB_SERVER: ${{ parameters.sechubServer }}
      SECHUB_USERID: ${{ parameters.sechubUserId }}
      SECHUB_APITOKEN: ${{ parameters.sechubApiToken }}


  - task: Bash@3
    displayName: Download SecHub Report
    condition: eq(${{parameters.downloadReport}}, true)
    inputs:
      targetType: inline
      script: |
        cd $(Build.SourcesDirectory)
        echo "Creating sechub reports directory"
        mkdir -p reports
        echo "listing pwd"
        pwd
        echo "${{ parameters.reportFilePath }} "
        sechub getReport  -project sbod  -reportformat html  -output ${{ parameters.reportFilePath }}.html
        sechub getReport  -project sbod  -reportformat json  -output ${{ parameters.reportFilePath }}.json
    env:
      SECHUB_SERVER: ${{ parameters.sechubServer }}
      SECHUB_USERID: ${{ parameters.sechubUserId }}
      SECHUB_APITOKEN: ${{ parameters.sechubApiToken }}

  - publish: $(Build.SourcesDirectory)/reports
    artifact: "SecHub Report"
    condition: eq(${{parameters.downloadReport}}, true)
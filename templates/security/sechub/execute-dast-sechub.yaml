parameters:
  - name: configPath
  - name: downloadReport
  - name: reportFilePath
  - name: sechubServer
  - name: sechubUserId
  - name: sechubApiToken
  - name:

steps:
  - template: "../generate-sbod-openapi-spec.yaml"

  - template: "./execute-sechub.yaml"
    parameters:
      configPath: ${{ variables.SAST_SCAN_CONFIG_PATH }}
      downloadReport: "true"
      reportFilePath: $(variables.SAST_REPORT_DOWNLOAD_PATH)
      sechubServer: ${{ variables.SECHUB_SERVER }}
      sechubUserId: ${{ variables.SECHUB_USERID }}
      sechubApiToken: $(PIDC1A9-sechub-api-token)
      continueOnError: false
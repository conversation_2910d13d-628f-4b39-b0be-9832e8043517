parameters:
  - name: sastScanConfigPath
  - name: sastReportDownloadPath
  - name: sechubServer
  - name: sechubUserId
  - name: azureSubscription
  - name: keyvaultName

steps:
  - checkout: self

  - checkout: sbod-core

  - checkout: sbod-ext

  - checkout: sbod-common

  - task: AzureKeyVault@2
    displayName: "Azure KeyVault: Get SecHub API token"
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      KeyVaultName: ${{ parameters.keyvaultName }}
      SecretsFilter: "PIDC1A9-sechub-api-token"

  - template: "../install-sechub.yaml"

  - template: "../execute-sechub.yaml"
    parameters:
      configPath: ${{ parameters.sastScanConfigPath }}
      downloadReport: "false"
      reportFilePath: $(parameters.sastReportDownloadPath)
      sechubServer: ${{ parameters.sechubServer }}
      sechubUserId: ${{ parameters.sechubUserId }}
      sechubApiToken: $(PIDC1A9-sechub-api-token)
      continueOnError: false
steps:
  - task: Bash@3
    displayName: Install Sechub scanner
    inputs:
      targetType: inline
      script: |
        echo "Installing SecHub"
        cd /tmp
        CLIENT_VERSION=$(curl -s https://mercedes-benz.github.io/sechub/latest/client-download.html | grep https://github.com/mercedes-benz/sechub/ | awk -F '-' '{print $NF}' | sed 's/.zip">//')
        curl -L -o sechub-cli.zip https://github.com/mercedes-benz/sechub/releases/download/v$CLIENT_VERSION-client/sechub-cli-$CLIENT_VERSION.zip
        unzip sechub-cli.zip
        sudo cp platform/linux-amd64/sechub /usr/local/bin      


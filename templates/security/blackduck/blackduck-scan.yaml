parameters:
  - name: projectName
  - name: repo
  - name: version
    default: latest
  - name: rapidScan
    type: boolean
    default: true
  - name: failOnSeverities
    type: boolean
    default: false

steps:
  - task: BlackDuckDetectTask@10
    displayName: "Scan: ${{ parameters.projectName }}"
    condition: succeededOrFailed()
    inputs:
      BlackDuckScaService: "black-duck-sc"
      DetectVersion: "latest"
      DetectArguments: >-
        --detect.project.name="${{ parameters.projectName }}"
        --detect.project.version.name="${{ parameters.version }}"
        --detect.source.path="${{ parameters.repo }}"
        ${{ iif(parameters.rapidScan, '--detect.blackduck.scan.mode=RAPID', '') }}
        ${{ iif(parameters.failOnSeverities, '--detect.policy.check.fail.on.severities=BLOCKER,CRITICAL,MAJOR', '') }}

parameters:
  - name: repoName
  - name: projectKey
  - name: projectName
  - name: sonarqubeToken

steps:
  - checkout: ${{ parameters.repoName }}

  - task: JavaToolInstaller@0
    inputs:
      versionSpec: "17"
      jdkArchitectureOption: "x64"
      jdkSourceOption: "PreInstalled"
  - task: DownloadSecureFile@1
    name: cert
    displayName: "Download certificate"
    inputs:
      secureFile: "keystore_Mercedes-Benz-Group-AG-SDP_20515.pfx"
  - task: Bash@3
    name: SonarDeploy
    inputs:
      targetType: "inline"
      script: |
        SONARVERSION=4.3.0.2102
        wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-$SONARVERSION-linux.zip
        unzip sonar-scanner-cli-$SONARVERSION-linux.zip
        sonar-scanner-$SONARVERSION-linux/bin/sonar-scanner -v

        export SONAR_SCANNER_OPTS='-Djavax.net.ssl.keyStorePassword=$(client-cert-keystore-pw) -Djavax.net.ssl.keyStoreType=pkcs12 -Djavax.net.ssl.keyStore=$(cert.secureFilePath)'
        echo $SONAR_SCANNER_OPTS

        sonar-scanner-$SONARVERSION-linux/bin/sonar-scanner -X \
        -Dsonar.host.url=https://sdp.i.mercedes-benz.com/sonar \
        -Dsonar.branch.name=main \
        -Dsonar.sources=. \
        -Dsonar.language=java \
        -Dsonar.login=${{ parameters.sonarqubeToken }} \
        -Dsonar.projectVersion=$(Build.SourceVersion) \
        -Dsonar.java.binaries=/home/<USER>/work/1/s \
        -Dsonar.coverage.exclusions=**/test/**/* \
        -Dsonar.projectKey=${{parameters.projectKey}} \
        -Dsonar.cfamily.build-wrapper-output.bypass=true \
        -Dsonar.projectName=${{parameters.projectName}} \

parameters:
  - name: jobName
  - name: jobDisplayName
    default: "Deploy Gateway"
  - name: vmImage
    default: "ubuntu-latest"
  - name: helmVersion
    default: "3.14.0"
  - name: subscription
    default: "ADO-IAMS-DaiVB IAM Services"
  - name: subscriptionVault
    default: "ADO-IAMS-DaiVB IAM Services"
  - name: akvName
  - name: acrName
    default: "daivbiams"
  - name: acrAzName
    default: "daivbiams.azurecr.io"
  - name: aksClusterResourceGroup
  - name: aksClusterName
  - name: keyVaultSecretsFilter
  - name: tokenGatewayNamespace
  - name: applicationNamespace
  - name: helmReleaseName
  - name: micGatewayChartValueFile
  - name: micGatewayChartValueOverrides
  - name: micGatewayChartName
    default: "oci://daivbiams.azurecr.io/helm/mic-gateway"
  - name: micGatewayChartVersion
    default: "0.0.8"
  - name: deployProceed
    type: boolean
    default: false

jobs:
  - job: ${{ parameters.jobName }}
    displayName: ${{ parameters.jobDisplayName }}
    pool:
      vmImage: ${{ parameters.vmImage }}
    steps:
      - checkout: self
        persistCredentials: true

      - template: gateway-deployment-steps/001-install-helm.yaml
        parameters:
          version: ${{ parameters.helmVersion }}

      - template: gateway-deployment-steps/002-secret-download.yaml
        parameters:
          subscription: ${{ parameters.subscriptionVault }}
          vaultName: ${{ parameters.akvName }}
          secretsFilter: ${{ parameters.keyVaultSecretsFilter }}

      - template: gateway-deployment-steps/003-add-acr-helm-repository.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          acr_name: ${{ parameters.acrName }}
          acr_az_name: ${{ parameters.acrAzName }}
          aks_sp_client_id: $(sp-sbod-clientid)
          aks_sp_client_secret: $(sp-sbod-clientsecret)

      - template: gateway-deployment-steps/004-aks-create-namespace.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.tokenGatewayNamespace }}

      - template: gateway-deployment-steps/005-optional-helm-delete.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.tokenGatewayNamespace }}
          release_name: ${{ parameters.helmReleaseName }}

      - template: gateway-deployment-steps/006-helm-upgrade.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.tokenGatewayNamespace }}
          release_name: ${{ parameters.helmReleaseName }}
          chart_name: ${{ parameters.micGatewayChartName }}
          chart_version: ${{ parameters.micGatewayChartVersion }}
          value_file: ${{ parameters.micGatewayChartValueFile }}
          value_overrides: ${{ parameters.micGatewayChartValueOverrides }}

      - template: gateway-deployment-steps/007-helm-upgrade-reflector.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}

      - template: gateway-deployment-steps/008-process-tokenmaster-client-id.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          vaultName: ${{ parameters.akvName }}

      - template: gateway-deployment-steps/009-add-secret-annotation.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.tokenGatewayNamespace }}
          app_namespace: ${{ parameters.applicationNamespace }}

      - template: gateway-deployment-steps/010-app-create-secrets.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          app_namespace: ${{ parameters.applicationNamespace }}
          namespace: ${{ parameters.tokenGatewayNamespace }}
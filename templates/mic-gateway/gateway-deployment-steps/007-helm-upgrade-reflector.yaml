parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name

steps:
  - task: CmdLine@2
    displayName: "Helm add repo"
    inputs:
      script: |
        helm repo add emberstack https://emberstack.github.io/helm-charts
        helm repo update

  - task: HelmDeploy@0
    displayName: upgrade reflector plugin
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      azureResourceGroup: ${{ parameters.cluster_rg }}
      kubernetesCluster: ${{ parameters.cluster_name }}
      namespace: "default"
      command: upgrade
      releaseName: "reflector"
      chartType: "Name"
      chartName: "emberstack/reflector"

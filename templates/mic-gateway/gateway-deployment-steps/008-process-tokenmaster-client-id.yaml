parameters:
  - name: subscription
  - name: vaultName

steps:
  - task: AzureCLI@2
    displayName: "Azure CLI: Print Secret Name"
    inputs:
      azureSubscription: "${{ parameters.subscription }}"
      scriptType: "bash"
      scriptLocation: "inlineScript"
      inlineScript: |
        tokenmasterClientId=$(az keyvault secret show --name "tokenmaster-sbod-clientid" --vault-name "${{ parameters.vaultName }}" --query "value")
        tokenmasterClientId=gw-clntcert-$(echo $tokenmasterClientId | tr '[:upper:]' '[:lower:]' | tr -d '"')
        echo $tokenmasterClientId
        echo "##vso[task.setvariable variable=clientId;]$tokenmasterClientId"

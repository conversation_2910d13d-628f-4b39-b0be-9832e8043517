parameters:
  - name: subscription
  - name: acr_name
  - name: acr_az_name
  - name: aks_sp_client_id
  - name: aks_sp_client_secret

steps:
  - task: AzureCLI@1
    displayName: "Azure CLI"
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      scriptLocation: inlineScript
      inlineScript: |
        echo ${{ parameters.aks_sp_client_secret }} | helm registry login ${{ parameters.acr_az_name }} --username ${{ parameters.aks_sp_client_id }} --password-stdin

#       Can test this by running the following command:
#       helm pull oci://daivbiams.azurecr.io/helm/mic-gateway --version 3.21.0

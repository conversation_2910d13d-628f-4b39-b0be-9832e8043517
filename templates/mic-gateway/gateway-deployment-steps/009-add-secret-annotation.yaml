parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: namespace
  - name: app_namespace

steps:
  - task: <PERSON><PERSON><PERSON><PERSON>@1
    name: annotateSecret
    displayName: "Add Reflector Annotations to Secret"
    inputs:
      connectionType: "Azure Resource Manager"
      azureSubscriptionEndpoint: "${{ parameters.subscription }}"
      azureResourceGroup: "${{ parameters.cluster_rg }}"
      kubernetesCluster: "${{ parameters.cluster_name }}"
      namespace: "${{ parameters.namespace }}"
      command: "annotate"
      arguments: >
        secret $(clientId)
        reflector.v1.k8s.emberstack.com/reflection-allowed="true"
        reflector.v1.k8s.emberstack.com/reflection-allowed-namespaces="${{ parameters.app_namespace }}, default"
        --overwrite=true

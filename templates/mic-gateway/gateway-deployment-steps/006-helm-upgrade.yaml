parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: namespace
  - name: release_name
  - name: chart_name
  - name: chart_version
  - name: value_file
  - name: value_overrides
    default: ''

steps:
  - task: HelmDeploy@0
    displayName: upgrade ${{ parameters.release_name }}
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      azureResourceGroup: ${{ parameters.cluster_rg }}
      kubernetesCluster: ${{ parameters.cluster_name }}
      namespace: ${{ parameters.namespace }}
      command: upgrade
      releaseName: ${{ parameters.release_name }}
      chartName: ${{ parameters.chart_name }}
      valueFile: ${{ parameters.value_file }}
      overrideValues: ${{ parameters.value_overrides }}
      resetValues: true
      arguments: "--version=${{ parameters.chart_version }}"
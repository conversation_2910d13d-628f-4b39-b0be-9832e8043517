parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: namespace
  - name: release_name

steps:
  - task: <PERSON><PERSON>Deploy@0
    displayName: delete helm release ${{ parameters.release_name }}
    condition: and(succeededOrFailed(), eq(variables.delete, true))
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      azureResourceGroup: ${{ parameters.cluster_rg }}
      kubernetesCluster: ${{ parameters.cluster_name }}
      namespace: ${{ parameters.namespace }}
      command: delete
      arguments: '-n ${{ parameters.namespace }} ${{ parameters.release_name }}'
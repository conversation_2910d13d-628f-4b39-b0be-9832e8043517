parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: namespace
  - name: app_namespace

steps:
  - task: <PERSON>ber<PERSON>es@1
    name: createSecretKubectlForAppNamespace
    displayName: "Create K8s secrets for ${{ parameters.app_namespace }} namespace"
    inputs:
      connectionType: "Azure Resource Manager"
      azureSubscriptionEndpoint: "${{ parameters.subscription }}"
      azureResourceGroup: "${{ parameters.cluster_rg }}"
      kubernetesCluster: "${{ parameters.cluster_name }}"
      namespace: "${{ parameters.app_namespace }}"
      command: "apply"
      useConfigurationFile: true
      inline: |
        apiVersion: v1
        kind: Secret
        type: Generic
        metadata:
          name: mic-gateway-clntcert
          annotations:
            reflector.v1.k8s.emberstack.com/reflects: "${{ parameters.namespace }}/$(clientId)"

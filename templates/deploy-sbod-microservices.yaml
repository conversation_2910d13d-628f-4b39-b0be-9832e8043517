parameters:
  - name: commonReleaseName
  - name: imageTag
  - name: chartReleaseName
  - name: branchName
  - name: azureSubscription
  - name: azureK8sResourceGroup
  - name: kubernetesCluster
  - name: helmVersion
  - name: chartPath
  - name: configFilePath
  - name: keyvaultName
  - name: repoName
  - name: namespace
  - name: skipTestCase
  - name: environment
  - name: acrName
  - name: buildAndDeploy
    default: true

steps:
  - template: deploy-microservices/prepare-environment.yaml
    parameters:
      repoName: ${{ parameters.repoName }}
      branchName: ${{ parameters.branchName }}
      azureSubscription: ${{ parameters.azureSubscription }}
      keyvaultName: ${{ parameters.keyvaultName }}

  - template: deploy-microservices/build-common.yaml
    parameters:
      commonReleaseName: ${{parameters.commonReleaseName}}
      branchName: ${{ parameters.branchName }}
      repoName: ${{ parameters.repoName }}
      skipTestCase: ${{ parameters.skipTestCase }}
      environment: ${{ parameters.environment }}

  - template: deploy-microservices/build-sbod-service.yaml
    parameters:
      commonReleaseName: ${{parameters.commonReleaseName}}
      imageTag: ${{ parameters.imageTag }}
      branchName: ${{ parameters.branchName }}
      repoName: ${{ parameters.repoName }}
      skipTestCase: ${{ parameters.skipTestCase }}
      environment: ${{ parameters.environment }}
      acrName: ${{ parameters.acrName }}

  - ${{ if parameters.buildAndDeploy }}:
      - template: ../templates/deploy-microservices/deploy-sbod-service.yaml
        parameters:
          imageTag: ${{ parameters.imageTag }}
          chartReleaseName: ${{ parameters.chartReleaseName }}
          azureSubscription: ${{ parameters.azureSubscription }}
          azureK8sResourceGroup: ${{ parameters.azureK8sResourceGroup }}
          kubernetesCluster: ${{ parameters.kubernetesCluster }}
          chartPath: ${{ parameters.chartPath }}
          configFilePath: ${{ parameters.configFilePath }}
          repoName: ${{ parameters.repoName }}
          namespace: ${{ parameters.namespace }}
          branchName: ${{ parameters.branchName }}

parameters:
  - name: jobName
  - name: jobDisplayName
    default: "Deploy nginx-ingress"
  - name: vmImage
    default: "ubuntu-latest"
  - name: helmVersion
    default: "3.14.0"
  - name: subscription
    default: "ADO-IAMS-DaiVB IAM Services"
  - name: subscriptionVault
    default: "ADO-IAMS-DaiVB IAM Services"

  - name: aksClusterResourceGroup
  - name: aksClusterName

  - name: nginxNamespace
  - name: nginxHelmReleaseName
  - name: nginxChartName
  - name: nginxChartVersion
  - name: nginxChartValueFile

  - name: deployProceed
    type: boolean
    default: false

jobs:
  - job: ${{ parameters.jobName }}
    displayName: ${{ parameters.jobDisplayName }}
    pool:
      vmImage: ${{ parameters.vmImage }}
    steps:
      - checkout: self
        persistCredentials: true

      - template: deployment-steps/001-get-ip-and-cluster-rg.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_name: ${{ parameters.aksClusterName }}
          value_file: ${{ parameters.nginxChartValueFile }}

      - template: deployment-steps/002-install-helm.yaml
        parameters:
          version: ${{ parameters.helmVersion }}

      - template: deployment-steps/003-install-kubectl.yaml

      - template: deployment-steps/004-add-helm-repo.yaml

      - template: deployment-steps/005-create-nginx-ns.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.nginxNamespace }}

      - template: deployment-steps/006-deploy-nginx.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.nginxNamespace }}
          release_name: ${{ parameters.nginxHelmReleaseName }}
          chart_name: ${{ parameters.nginxChartName }}
          chart_version: ${{ parameters.nginxChartVersion }}
          value_file: ${{ parameters.nginxChartValueFile }}

parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: namespace

steps:
  - task: <PERSON><PERSON><PERSON><PERSON><PERSON>nstalle<PERSON>@0
  
  - task: <PERSON>bernetes@1
    displayName: "Create NS ingress-nginx"
    condition: succeeded()
    inputs:
      connectionType: "Azure Resource Manager"
      azureSubscriptionEndpoint: "${{ parameters.subscription }}"
      azureResourceGroup: "${{ parameters.cluster_rg }}"
      kubernetesCluster: "${{ parameters.cluster_name }}"
      command: apply
      useConfigurationFile: true
      configurationType: inline
      inline: |
        apiVersion: v1
        kind: Namespace
        metadata:
          name: ${{ parameters.namespace }}

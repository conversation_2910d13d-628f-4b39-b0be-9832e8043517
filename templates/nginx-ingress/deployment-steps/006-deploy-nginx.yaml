parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: value_file
  - name: namespace
  - name: release_name
  - name: chart_name
  - name: chart_version

steps:
  - task: HelmDeploy@0
    displayName: 'helm install & upgrade ingress'
    condition: succeeded()
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscription: '${{ parameters.subscription }}'
      azureResourceGroup: '$(ClusterRG)'
      kubernetesCluster: '${{ parameters.cluster_name }}'
      namespace: ${{ parameters.namespace }}
      command: upgrade
      chartType: 'Name'
      chartName: ${{ parameters.chart_name }}
      releaseName: ${{ parameters.release_name }}
      overrideValues: 'controller.service.loadBalancerIP=$(pubIP),controller.service.annotations."service\.beta\.kubernetes\.io/azure-load-balancer-resource-group"=Network-SBOD-Services-PublicIPs,controller.service.annotations."service\.beta\.kubernetes\.io/azure-pip-name"=sbod_ece_1_v1_weu_IP_IN'
      valueFile: '${{ parameters.value_file }}'
      install: true
      resetValues: true
      arguments: '--version ${{ parameters.chart_version }}'

parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: value_file
  - name: namespace
  - name: release_name
  - name: chart_name
  - name: chart_version

steps:
  - task: HelmDeploy@0
    displayName: 'helm install & upgrade ingress'
    condition: succeeded()
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscription: '${{ parameters.subscription }}'
      azureResourceGroup: '$(ClusterRG)'
      kubernetesCluster: '${{ parameters.cluster_name }}'
      namespace: ${{ parameters.namespace }}
      command: upgrade
      chartType: 'Name'
      chartName: ${{ parameters.chart_name }}
      releaseName: ${{ parameters.release_name }}
      overrideValues: 'controller.service.loadBalancerIP=$(pubIP)'
      valueFile: '${{ parameters.value_file }}'
      install: true
      resetValues: true
      arguments: '--version ${{ parameters.chart_version }}'

parameters:
  - name: azureSubscription
  - name: vaultName
  - name: hostName
  - name: requestParam<PERSON>ey
  - name: requestParam
  - name: requestBody
  - name: endPointName
  - name: method
  - name: secretName
  - name: secretNamespace
  - name: azureK8sResourceGroup
  - name: kubernetesCluster

steps:
  - task: <PERSON><PERSON><PERSON><PERSON><PERSON>nstalle<PERSON>@0
    inputs:
      kubeloginVersion: "latest"

  - task: AzureKeyVault@2
    displayName: "Azure Key Vault: Get Service Principal Id and Secret"
    condition: succeeded()
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      KeyVaultName: "${{ parameters.vaultName }}"
      SecretsFilter: "sp-sbod-clientid, sp-sbod-clientsecret"

  - task: AzureCLI@2
    displayName: "Azure CLI: Get Kubernetes Credentials"
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      scriptType: "bash"
      scriptLocation: "inlineScript"
      inlineScript: |
        # Ensure we have the correct Kubernetes context
        az aks get-credentials --resource-group ${{ parameters.azureK8sResourceGroup }} --name ${{ parameters.kubernetesCluster }}

        kubelogin convert-kubeconfig -l spn --client-id $(sp-sbod-clientid) --client-secret $(sp-sbod-clientsecret) 

        kubectl get secret ${{ parameters.secretName }} -n ${{ parameters.secretNamespace }} -o jsonpath="{.data.tls\.crt}" | base64 --decode > /tmp/tmp.cert.crt
        kubectl get secret ${{ parameters.secretName }} -n ${{ parameters.secretNamespace }} -o jsonpath="{.data.tls\.key}" | base64 --decode > /tmp/tmp.private.key

  - task: Bash@3
    displayName: Create the URL based on the presence of request Parameter
    inputs:
      targetType: inline
      script: |
        if [[ -n "${{ parameters.requestParamKey }}" ]]; then
          hostName="${{ parameters.hostName }}?${{ parameters.requestParamKey }}=${{ parameters.requestParam }}"
        else
          hostName=${{ parameters.hostName}}
        fi
        echo "##vso[task.setvariable variable=url;]$hostName"

  - task: Bash@3
    displayName: Create the request body if exists
    inputs:
      targetType: inline
      script: |
        if [[ -n "${{ parameters.requestBody }}" ]]; then
          requestBody="-d '${{parameters.requestBody}}' "
        else
          requestBody=""
        fi
        echo "##vso[task.setvariable variable=reqBody;]$requestBody"

  - task: AzureKeyVault@2
    displayName: "Azure Key Vault: Get tokenMasterClientId and tokenMasterClientSecret"
    condition: succeeded()
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      KeyVaultName: "${{ parameters.vaultName }}"
      SecretsFilter: "tokenmaster-sbod-clientid, tokenmaster-sbod-clientsecret"

  - task: CmdLine@2
    displayName: Get the token for MIC Gateway (SBOD-DEV Environment)
    inputs:
      script: |
        response=$(curl --request POST \
        --url https://ssoalpha.dvb.corpinter.net/v1/token \
        -u $(tokenmaster-sbod-clientid):$(tokenmaster-sbod-clientsecret) \
        --header 'Content-Type: application/x-www-form-urlencoded' \
        --data 'scope=openid profile email mic:env:prod groups audience:server:client_id:$(tokenmaster-sbod-clientid)' \
        --data 'grant_type=client_credentials' )
        token=$(echo $response | /usr/bin/jq --raw-output '.access_token')
        echo "##vso[task.setvariable variable=accessMICToken;]$token"

  - task: CmdLine@2
    displayName: Get the token for MIC Gateway (SBOD-DEV Environment)
    inputs:
      script: |
        response=$(curl --request POST \
        --url https://ssoalpha.dvb.corpinter.net/v1/token \
        -u $(tokenmaster-sbod-clientid):$(tokenmaster-sbod-clientsecret) \
        --header 'Content-Type: application/x-www-form-urlencoded' \
        --data 'scope=openid profile groups audience:server:client_id:DAIVBADM_MICTM_EMEA_PROD_00968' \
        --data 'grant_type=client_credentials' )
        token=$(echo $response | /usr/bin/jq --raw-output '.access_token')
        echo "##vso[task.setvariable variable=accessMailToken;]$token"

  - task: CmdLine@2
    displayName: Call the endpoint for the Check Cert Expiry Endpoint
    inputs:
      script: |
        echo "Request Body: $(reqBody)"
        response=$(curl --request ${{ parameters.method }} \
          -s -o /dev/null -w "%{http_code}" \
          --url '$(url)' \
          --header 'Authorization: Bearer $(accessMICToken)'\
          --header 'Content-Type: application/json' \
          --header 'x-fmsId: MBC.CBNG.SIXTH' \
          --header 'x-requestId: aea8777a-8de4-44f6-bc52-ca8e44c4e8a6' \
          --header 'x-responseId: dd3b8fa0-4cc9-4ae9-ab81-f6bb96164070' \
          --header 'x-sbodId: sbod-azureDevOps' \
          $(reqBody) \
          --cert /tmp/tmp.cert.crt \
          --key /tmp/tmp.private.key )
        echo "##vso[task.setvariable variable=responseStatus;]$response"
        echo "Call response status code $response successfully"

  - task: CmdLine@2
    displayName: Send a successful email if the endpoint returns 200
    condition: and(succeeded(), eq(variables['responseStatus'], '200'))
    inputs:
      script: |
        echo "${{ parameters.endPointName }} Endpoint is currently up and running"

  # SBOD Internal Email: <EMAIL>
  - task: CmdLine@2
    displayName: Send a failed email if the endpoint not returns 200
    condition: and(succeeded(), ne(variables['responseStatus'], '200'))
    inputs:
      script: |
        curl --request POST \
          --url https://mma.query.api.dvb.corpinter.net/mail \
          --header "Authorization: Bearer $(accessMailToken) " \
          --header 'Content-Type: application/json' \
          --cert /tmp/tmp.cert.crt \
          --key /tmp/tmp.private.key \
          --data '{ "Messages":[
                {
                    "To": [
                            {
                                "Email": "<EMAIL>",
                                "Name": "SBOD Internal Team"
                            }
                    ],
                    "Subject": "[Urgent] ${{ parameters.endPointName }} Endpoint has Issue - $(responseStatus)",
                    "TextPart": "Hi, Team. The email is to inform you that the ${{ parameters.endPointName }} Endpoint ( $(url) ) is currently down and returning a status code of $(responseStatus)."
                }
            ]
        }'

  - task: CmdLine@2
    displayName: Remove temporary files
    inputs:
      script: |
        rm -f /tmp/tmp.cert.crt
        rm -f /tmp/tmp.private.key
        echo "Remove temporary files successfully"

parameters:
  - name: AzureSubs
  - name: ClusterName
  - name: IngressConfigPath
  - name: IngressChartVersion

  - name: monPromConfigPath
  - name: monPromChartVersion

  - name: monBlackConfigPath
  - name: monBlackChartVersion

  - name: ddConfigPath
  - name: ddChartVersion
  - name: DatadogResourceSize
  - name: IngressNS
  - name: DatadogNS
  - name: DatadogName

  - name: TokenmasterConfigPath
  - name: TokenmasterNs
  - name: TokenmasterName

  - name: reinstallIngress
  - name: reinstallProm
  - name: reinstallDD
  - name: reinstallTokenmaster
  - name: VaultName

steps:
- task: AzureCLI@1
  displayName: 'Get IP and Cluster RG'
  inputs:
    azureSubscription: '${{ parameters.AzureSubs }}'
    scriptPath: '$(System.DefaultWorkingDirectory)/scripts/getIPandFQDN.sh'
    arguments: '${{ parameters.ClusterName }}'
    workingDirectory: '$(System.DefaultWorkingDirectory)/scripts'
    failOnStandardError: true

# Change the publicIP/FQDN/Heartbeat in the prometheus config file
- bash: |
    CLNM=${{ parameters.ClusterName }}
    OPGE=$(echo $CLNM | awk '{ print toupper($0) }' | sed -e "s/_/-/g" | cut -f1-5 -d "-")
    echo "========================================"
    echo "OpsGenie heartbeat name: $OPGE"
    sed -ie 's/111.222.333.444/$(pubIP)/g' $(System.DefaultWorkingDirectory)/${{ parameters.monPromConfigPath }}
    sed -ie 's/CLUSTERFQDN/$(ClusterFQDN)/g' $(System.DefaultWorkingDirectory)/${{ parameters.monPromConfigPath }}
    sed -ie "s/__OPSGENIE__/$OPGE/g" $(System.DefaultWorkingDirectory)/${{ parameters.monPromConfigPath }}
    echo "========================================"
    echo "IP Checks in the config file:"
    cat $(System.DefaultWorkingDirectory)/${{ parameters.monPromConfigPath }} | grep ":443"
    echo "========================================"
    echo "Heartbeat name check in the config file:"
    cat $(System.DefaultWorkingDirectory)/${{ parameters.monPromConfigPath }} | grep "$OPGE"
    echo "========================================"

- task: HelmInstaller@0
  displayName: 'Install Helm 3.9.2'
  inputs:
    helmVersion: 3.9.2
    checkLatestHelmVersion: false

- task: KubectlInstaller@0
  displayName: Kubectl installer
  inputs:
    kubectlVersion: latest

- task: CmdLine@2
  displayName: 'Helm add repo'
  inputs:
    script: |
      helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
      helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
      helm repo add datadog https://helm.datadoghq.com
      helm repo update

- task: AzureKeyVault@2
  displayName: 'Azure Key Vault: Get SP client and secret'
  inputs:
    azureSubscription: '${{ parameters.AzureSubs }}'
    KeyVaultName: '${{ parameters.VaultName }}'
    SecretsFilter: 'sp-ece-nprweub-sbod-clientid,sp-ece-nprweub-sbod-clientsecret,tokenmaster-nonprod-clientsecret,tokenmaster-nonprod-clientid'
  
- task: Kubernetes@1
  displayName: 'Create NS monitoring'
  condition: and(succeeded(), eq('${{ parameters.reinstallProm }}', 'true'))
  inputs:
    connectionType: 'Azure Resource Manager'
    azureSubscriptionEndpoint: '${{ parameters.AzureSubs }}'
    azureResourceGroup: '$(ClusterRG)'
    kubernetesCluster: '${{ parameters.ClusterName }}'
    command: apply
    useConfigurationFile: true
    configurationType: inline
    inline: |
      apiVersion: v1
      kind: Namespace
      metadata:
        name: monitoring

- task: HelmDeploy@0
  displayName: 'helm install_upgrade prometheus'
  condition: and(succeeded(), eq('${{ parameters.reinstallProm }}', 'true'))
  inputs:
    connectionType: 'Azure Resource Manager'
    azureSubscription: '${{ parameters.AzureSubs }}'
    azureResourceGroup: '$(ClusterRG)'
    kubernetesCluster: '${{ parameters.ClusterName }}'
    namespace: 'monitoring'
    command: upgrade
    chartType: 'Name'
    chartName: 'prometheus-community/kube-prometheus-stack'
    releaseName: 'prometheus'
    valueFile: '$(System.DefaultWorkingDirectory)/${{ parameters.monPromConfigPath }}'
    install: true
    resetValues: true
    arguments: "--version=${{ parameters.monPromChartVersion }}"

- task: HelmDeploy@0
  displayName: 'helm install_upgrade blackbox'
  condition: and(succeeded(), eq('${{ parameters.reinstallProm }}', 'true'))
  inputs:
    connectionType: 'Azure Resource Manager'
    azureSubscription: '${{ parameters.AzureSubs }}'
    azureResourceGroup: '$(ClusterRG)'
    kubernetesCluster: '${{ parameters.ClusterName }}'
    namespace: 'monitoring'
    command: upgrade
    chartType: 'Name'
    chartName: 'prometheus-community/prometheus-blackbox-exporter'
    releaseName: 'blackbox'
    valueFile: '$(System.DefaultWorkingDirectory)/${{ parameters.monBlackConfigPath }}'
    install: true
    resetValues: true
    arguments: '--version=${{ parameters.monBlackChartVersion }}'

- task: Kubernetes@1
  displayName: 'Create NS ingress-nginx'
  condition: and(succeeded(), eq('${{ parameters.reinstallIngress }}', 'true'))
  inputs:
    connectionType: 'Azure Resource Manager'
    azureSubscriptionEndpoint: '${{ parameters.AzureSubs }}'
    azureResourceGroup: '$(ClusterRG)'
    kubernetesCluster: '${{ parameters.ClusterName }}'
    command: apply
    useConfigurationFile: true
    configurationType: inline
    inline: |
      apiVersion: v1
      kind: Namespace
      metadata:
        name: ingress-nginx

- task: HelmDeploy@0
  displayName: 'helm install_upgrade ingress'
  condition: and(succeeded(), eq('${{ parameters.reinstallIngress }}', 'true'))
  inputs:
    connectionType: 'Azure Resource Manager'
    azureSubscription: '${{ parameters.AzureSubs }}'
    azureResourceGroup: '$(ClusterRG)'
    kubernetesCluster: '${{ parameters.ClusterName }}'
    namespace: 'ingress-nginx'
    command: upgrade
    chartType: 'Name'
    chartName: 'ingress-nginx/ingress-nginx'
    releaseName: 'ingress-nginx'
    overrideValues: 'controller.service.loadBalancerIP="$(pubIP)"'
    valueFile: '$(System.DefaultWorkingDirectory)/${{ parameters.IngressConfigPath }}'
    install: true
    resetValues: true
    arguments: '--version=${{ parameters.IngressChartVersion }}'

- task: AzureCLI@1
  displayName: 'Get Datadog Config'
  condition: and(succeeded(), eq('${{ parameters.reinstallDD }}', 'true'))
  inputs:
    azureSubscription: '${{ parameters.AzureSubs }}'
    scriptPath: '$(System.DefaultWorkingDirectory)/scripts/get_datadog_tags.sh'
    arguments: '$(ClusterRG) ${{ parameters.ClusterName }} ${{ parameters.DatadogResourceSize }} ${{ parameters.IngressNS }}'
    workingDirectory: '$(System.DefaultWorkingDirectory)/scripts'

- task: AzureKeyVault@2
  displayName: 'Azure Key Vault: Get Datadog Key'
  inputs:
    azureSubscription: '${{ parameters.AzureSubs }}'
    KeyVaultName: '${{ parameters.VaultName }}'
    SecretsFilter: 'tm-datadog-api-key,tm-datadog-app-key'

- task: AzureCLI@1
  displayName: 'Delete previous datadog deployment'
  condition: and(succeeded(), eq('${{ parameters.reinstallDD }}', 'true'))
  inputs:
    azureSubscription: '${{ parameters.AzureSubs }}'
    scriptPath: '$(System.DefaultWorkingDirectory)/scripts/helm_delete_datadog.sh'
    arguments: '${{ parameters.ClusterName }} $(ClusterRG) ${{ parameters.DatadogNS }} ${{ parameters.DatadogName }}'
    workingDirectory: '$(System.DefaultWorkingDirectory)/terraform/scripts'

- task: Kubernetes@1
  displayName: 'Create NS datadog'
  condition: and(succeeded(), eq('${{ parameters.reinstallDD }}', 'true'))
  inputs:
    connectionType: 'Azure Resource Manager'
    azureSubscriptionEndpoint: '${{ parameters.AzureSubs }}'
    azureResourceGroup: '$(ClusterRG)'
    kubernetesCluster: '${{ parameters.ClusterName }}'
    command: apply
    useConfigurationFile: true
    configurationType: inline
    inline: |
      apiVersion: v1
      kind: Namespace
      metadata:
        name: datadog

- task: HelmDeploy@0
  displayName: 'Helm deploy datadog'
  condition: and(succeeded(), eq('${{ parameters.reinstallDD }}', 'true'))
  inputs:
    connectionType: 'Azure Resource Manager'
    azureSubscription: '${{ parameters.AzureSubs }}'
    azureResourceGroup: '$(ClusterRG)'
    kubernetesCluster: '${{ parameters.ClusterName }}'
    namespace: 'datadog'
    command: upgrade
    chartType: 'Name'
    chartName: 'datadog/datadog'
    releaseName: 'datadog'
    valueFile: '$(System.DefaultWorkingDirectory)/${{ parameters.ddConfigPath }}'
    overrideValues: |
      datadog.clusterName=$(datadogClustername)
      datadog.tags[0]=env:$(stageTag)
      datadog.tags[1]=region:$(regionTag)
      datadog.tags[2]=geo:$(geoTag)
      datadog.tags[3]=mic_project:$(datadogProject)
      datadog.tags[4]=mic_department:$(datadogDepartment)
      datadog.apiKey=$(tm-datadog-api-key)
      datadog.appKey=$(tm-datadog-app-key)
    install: true
    arguments: '--version ${{ parameters.ddChartVersion }} -f $(System.DefaultWorkingDirectory)/scripts/mod_values.yml'


# - task: Kubernetes@1
#   displayName: 'Create NS tokenmaster'
#   condition: and(succeeded(), eq('${{ parameters.reinstallTokenmaster }}', 'true'))
#   inputs:
#     connectionType: 'Azure Resource Manager'
#     azureSubscriptionEndpoint: '${{ parameters.AzureSubs }}'
#     azureResourceGroup: '$(ClusterRG)'
#     kubernetesCluster: '${{ parameters.ClusterName }}'
#     command: apply
#     useConfigurationFile: true
#     configurationType: inline
#     inline: |
#       apiVersion: v1
#       kind: Namespace
#       metadata:
#         name: ${{ parameters.TokenmasterNs }}

# # Use File Method to deploy Tokenmaster Gateway
# - task: HelmDeploy@0
#   displayName: 'Helm deploy tokenmaster-gateway'
#   condition: and(succeeded(), eq('${{ parameters.reinstallTokenmaster }}', 'true'))
#   inputs:
#     connectionType: 'Azure Resource Manager'
#     azureSubscription: '${{ parameters.AzureSubs }}'
#     azureResourceGroup: '$(ClusterRG)'
#     kubernetesCluster: '${{ parameters.ClusterName }}'
#     namespace: '${{ parameters.TokenmasterNs }}'
#     command: upgrade
#     chartType: FilePath
#     chartPath: '$(System.DefaultWorkingDirectory)/mic-gateway'
#     valueFile: '$(System.DefaultWorkingDirectory)/${{ parameters.TokenmasterConfigPath }}'
#     releaseName: 'tokenmaster'
#     overrideValues: |
#       global.clientid=$(tokenmaster-nonprod-clientid)
#       global.clientsecret=$(tokenmaster-nonprod-clientsecret)
#     install: true
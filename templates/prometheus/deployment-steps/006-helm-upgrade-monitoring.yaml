parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name
  - name: value_file
  - name: namespace
  - name: release_name
  - name: chart_name
  - name: chart_version

steps:
  - task: He<PERSON><PERSON>eploy@0
    displayName: 'helm install & upgrade prometheus'
    condition: succeeded()
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscription: '${{ parameters.subscription }}'
      azureResourceGroup: '${{ parameters.cluster_rg }}'
      kubernetesCluster: '${{ parameters.cluster_name }}'
      namespace: '${{ parameters.namespace}}'
      command: upgrade
      chartType: 'Name'
      chartName: '${{ parameters.chart_name}}'
      releaseName: '${{ parameters.release_name}}'
      valueFile: '${{ parameters.value_file}}'
      install: true
      resetValues: true
      arguments: "--version=${{ parameters.chart_version }}"

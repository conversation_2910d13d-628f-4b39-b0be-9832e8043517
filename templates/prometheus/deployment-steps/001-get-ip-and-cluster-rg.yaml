parameters:
  - name: subscription
  - name: cluster_name
  - name: value_file

steps:
  - task: AzureCLI@1
    displayName: 'Get IP and Cluster RG'
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      scriptPath: '$(System.DefaultWorkingDirectory)/scripts/getIPandFQDN.sh'
      arguments: '${{ parameters.cluster_name }}'
      workingDirectory: '$(System.DefaultWorkingDirectory)/scripts'
      failOnStandardError: true

  # Change the publicIP/FQDN/Heartbeat in the prometheus config file
  - bash: |
      CLNM=${{ parameters.cluster_name }}
      OPGE=$(echo $CLNM | awk '{ print toupper($0) }' | sed -e "s/_/-/g" | cut -f1-5 -d "-")
      echo "========================================"
      echo "OpsGenie heartbeat name: $OPGE"
      sed -ie 's/111.222.333.444/$(pubIP)/g' $(System.DefaultWorkingDirectory)/${{ parameters.value_file }}
      sed -ie 's/CLUSTERFQDN/$(ClusterFQDN)/g' $(System.DefaultWorkingDirectory)/${{ parameters.value_file }}
      sed -ie "s/__OPSGENIE__/$OPGE/g" $(System.DefaultWorkingDirectory)/${{ parameters.value_file }}
      echo "========================================"
      echo "IP Checks in the config file:"
      cat $(System.DefaultWorkingDirectory)/${{ parameters.value_file }} | grep ":443"
      echo "========================================"
      echo "Heartbeat name check in the config file:"
      cat $(System.DefaultWorkingDirectory)/${{ parameters.value_file }} | grep "$OPGE"
      echo "========================================"
parameters:
  - name: subscription
  - name: cluster_rg
  - name: cluster_name

steps:
  - task: <PERSON><PERSON>oginInstaller@0

  - task: <PERSON>bernetes@1
    displayName: 'Create NS monitoring'
    condition: succeeded()
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscriptionEndpoint: '${{ parameters.subscription }}'
      azureResourceGroup: '${{ parameters.cluster_rg }}'
      kubernetesCluster: '${{ parameters.cluster_name }}'
      command: apply
      useConfigurationFile: true
      configurationType: inline
      inline: |
        apiVersion: v1
        kind: Namespace
        metadata:
          name: monitoring
parameters:
  - name: envName
  - name: regName
  - name: VarFolder
  - name: VaultName
  - name: VaultSecret1
  - name: VaultSecret2
  - name: ServiceConnection
  - name: AzureSubscriptionId
  - name: AzureTenantId
  - name: ResourceGroupName
  - name: ResourceGroupLocation
  - name: StorageAccountName
  - name: StorageContainerName
  - name: TfstateBlobName
  - name: varDir
  - name: Preview

steps:
  - task: AzureKeyVault@2
    displayName: "Azure Key Vault - Get secrets"
    inputs:
      ConnectedServiceName: "${{ parameters.ServiceConnection }}"
      KeyVaultName: "${{ parameters.VaultName }}"
      SecretsFilter: "${{ parameters.VaultSecret1 }},${{ parameters.VaultSecret2 }}"

  - template: general/install-opentofu.yml

  - task: CopyFiles@2
    displayName: "Copy var file to source dir"
    condition: succeeded()
    inputs:
      SourceFolder: "$(System.DefaultWorkingDirectory)/${{ parameters.varDir }}${{ parameters.envName }}/${{ parameters.VarFolder }}"
      Contents: "*.tfvars"
      TargetFolder: "$(System.DefaultWorkingDirectory)/terraform/postgresflex"
      OverWrite: true

  - task: AzureCLI@1
    displayName: "Create storage account"
    condition: succeeded()
    inputs:
      connectedServiceNameARM: "${{ parameters.ServiceConnection }}"
      scriptPath: "$(System.DefaultWorkingDirectory)/scripts/storage_account.sh"
      arguments: "${{ parameters.StorageAccountName }} ${{ parameters.ResourceGroupName }} ${{ parameters.ResourceGroupLocation }} ${{ parameters.StorageContainerName }}"
      workingDirectory: "$(System.DefaultWorkingDirectory)/scripts/"

  - bash: |
      cd '$(System.DefaultWorkingDirectory)/terraform/postgresflex'
      if [ "${{ parameters.regName }}" == "china" ]; then 
        sed -ie 's/public/china/g' main.tf
        echo "Deploying to Azure China cloud!!!"
      else
        echo "Deploying to Azure Public cloud!!!"  
      fi
    displayName: "Set region (Public/China)"

  - bash: |
      tofu init \
       -backend-config="resource_group_name=${{ parameters.ResourceGroupName }}" \
       -backend-config="storage_account_name=${{ parameters.StorageAccountName }}" \
       -backend-config="container_name=${{ parameters.StorageContainerName }}" \
       -backend-config="key=${{ parameters.TfstateBlobName }}"
    displayName: "OpenTofu Init"
    workingDirectory: "$(System.DefaultWorkingDirectory)/terraform/postgresflex"
    env:
      ARM_SUBSCRIPTION_ID: ${{ parameters.AzureSubscriptionId }}
      ARM_TENANT_ID: ${{ parameters.AzureTenantId }}
      ARM_CLIENT_ID: $(${{ parameters.VaultSecret1 }})
      ARM_CLIENT_SECRET: $(${{ parameters.VaultSecret2 }})

  - bash: |
      tofu plan -out out.plan -lock=true
    displayName: "OpenTofu Plan"
    workingDirectory: "$(System.DefaultWorkingDirectory)/terraform/postgresflex"
    env:
      ARM_SUBSCRIPTION_ID: ${{ parameters.AzureSubscriptionId }}
      ARM_TENANT_ID: ${{ parameters.AzureTenantId }}
      ARM_CLIENT_ID: $(${{ parameters.VaultSecret1 }})
      ARM_CLIENT_SECRET: $(${{ parameters.VaultSecret2 }})

  - bash: |
      tofu apply -lock=true out.plan
    displayName: "OpenTofu Apply"
    condition: and(succeeded(), eq('${{ parameters.Preview }}', false))
    workingDirectory: "$(System.DefaultWorkingDirectory)/terraform/postgresflex"
    env:
      ARM_SUBSCRIPTION_ID: ${{ parameters.AzureSubscriptionId }}
      ARM_TENANT_ID: ${{ parameters.AzureTenantId }}
      ARM_CLIENT_ID: $(${{ parameters.VaultSecret1 }})
      ARM_CLIENT_SECRET: $(${{ parameters.VaultSecret2 }})

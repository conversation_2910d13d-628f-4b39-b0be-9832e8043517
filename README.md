# SBOD Infrastructure

Pipelines and templates for infrastucture setup and deployment.

## Tools required for local development:

1. Homebrew

```sh
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

2. Task

```sh
brew install go-task
```

3. <PERSON><PERSON>: https://podman.io/docs/installation

4. Java JDK: Can be installed via IntelliJ or manually

## Database Setup using Task

Run these commands in the `./dev-tools/taskfile` folder to:

### Setup Database container

```sh
task start:setup
```

### Teardown Database container, if needed

```sh
task start:teardown
```

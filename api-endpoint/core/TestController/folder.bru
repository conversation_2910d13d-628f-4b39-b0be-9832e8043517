meta {
  name: <PERSON><PERSON><PERSON><PERSON><PERSON>
}

script:pre-request {
  const axios = require("axios");
  const url = require("url");
  const btoa = require("btoa");
  
  var creds = btoa(bru.getEnvVar("tokenmaster_client_id")+":"+bru.getEnvVar("tokenmaster_secret")); 
  
  // Setting up the URL from the original code
  const path = "https://ssoalpha.dvb.corpinter.net/v1/token";
  
  // Create encoded parameters matching the original code
  const params = new url.URLSearchParams({
    scope: "openid profile email mic:env:prod groups audience:server:client_id",
    grant_type: "client_credentials"
  });
  
  // Using the same authorization header as in the original code
  const headers = {
    "Authorization": "Basic " + creds,
    "Content-Type": "application/x-www-form-urlencoded"
  };
  
  // Making the POST request
  try {
    const response = await axios.post(path, params.toString(), {headers: headers});
    console.log(response.data);
    // Optionally store the token if needed
    bru.setVar("access_token", response.data.access_token);
  } catch (error) {
    console.error(error);
  }
}

meta {
  name: Prepare DK Sharing
  type: http
  seq: 16
}

post {
  url: {{base_url}}/core/api/v1/digitalkeys/prepareDKSharing
  body: json
  auth: bearer
}

headers {
  x-requestId: aea8777a-8de4-44f6-bc52-ca8e44c4e8a6
  x-fmsId: MBC.CBNG.SIXTH
  x-sbodId: sbod
  x-responseId: dd3b8fa0-4cc9-4ae9-ab81-f6bb96164070
  Content-Type: application/json
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "vehicleId": "WDBEA51E0MB336315",
    "accountIdHash": "C8645830202EFEB53427A6D75F15C85E78A5195307E2351858349AB9",
    "fmsSharingId": "5bc05ff5-9ae4-4b8e-9d16-62593c401ddb",
    "dkEntitlements": {
      "friendlyName": "<PERSON>'s Phone",
      "rights": 0,
      "notBefore": "2024-04-23T06:27:55.197Z",
      "notAfter": "2024-04-23T06:27:55.197Z"
    }
  }
}

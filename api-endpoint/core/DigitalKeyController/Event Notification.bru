meta {
  name: Event Notification
  type: http
  seq: 11
}

post {
  url: {{base_url}}/core/api/v1/digitalkeys/eventNotification
  body: json
  auth: bearer
}

headers {
  x-requestId: aea8777a-8de4-44f6-bc52-ca8e44c4e8a6
  x-fmsId: MBC.CBNG.SIXTH
  Content-Type: application/json
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "vehicleId": "WDDGF4HB9DR267526",
    "eventType": "INFLEETING_STARTED"
  }
}

meta {
  name: -VPN- Create Owner Key
  type: http
  seq: 9
}

post {
  url: {{base_url}}/core/api/v1/digitalkeys/createOwnerKey
  body: json
  auth: bearer
}

headers {
  x-requestId: aea8777a-8de4-44f6-bc52-ca8e44c4e8a6
  x-fmsId: MBC.CBNG.SIXTH
  x-sbodId: sbod
  x-responseId: dd3b8fa0-4cc9-4ae9-ab81-f6bb96164070
  Content-Type: application/json
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "FMSID": "MBCS.ORG",
    "vehicleId": "2d71a5c2b9b828d7",
    "DK_CREATION_DATA": {
      "endpointConfiguration": "V-OD-FW",
      "vehicleIdentifier": "2d71a5c2b9b828d7",
      "endpointIdentifier": "48c018842a5d7778fa4433e23bdfb1",
      "instanceCAIdentifier": "a8b6a5fc8d26c24b0fb88ef572e35d",
      "digitalKeyOptionGroup1": "11110101",
      "digitalKeyOptionGroup2": "00111110",
      "protocolVersion": "1.0",
      "vehiclePublicKey": "aa7cb5f59a8c194b19e4b73afa0a7e793c0ed64320eb72292cf1d4cb934e98b8d411b64eca86d439608f97ed6e8ed56be2e202b2fdcad9e3420fceac1e341b1767",
      "authorizedPublicKeys": [
        "FK-123",
        "FK-456",
        "FK-678"
      ],
      "slotIdentifier": "12b6293bbc52",
      "notBefore": "2023-08-01T00:00:00Z",
      "notAfter": "2023-08-31T00:00:00Z",
      "counterLimit": 0,
      "privateMailboxSize": 0,
      "confidentialMailboxSize": 0
    }
  }
}

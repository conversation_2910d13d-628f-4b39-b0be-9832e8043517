meta {
  name: InFleet
  type: http
  seq: 14
}

post {
  url: {{base_url}}/core/api/v1/digitalkeys/inFleet
  body: json
  auth: bearer
}

headers {
  x-requestId: aea8777a-8de4-44f6-bc52-ca8e44c4e8a6
  x-fmsId: MBC.CBNG.SIXTH
  x-sbodId: sbod-insomia
  x-responseId: dd3b8fa0-4cc9-4ae9-ab81-f6bb96164070
  Content-Type: application/json
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "vehicleId": "19UUA765X7A035631",
    "vehicleOEMIdentifier": "MBAG",
    "proofOfOwnership": ""
  }
}

meta {
  name: Send Email via MIC SMTP
  type: http
  seq: 7
}

post {
  url: https://mma.query.api.dvb.corpinter.net/mail
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  User-Agent: insomnia/9.2.0
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  { "Messages":[
          {
              "To": [
                      {
                          "Email": "<EMAIL>",
                          "Name": "Bao Ren"
                      }
              ],
              "Subject": "Checking Cert Expiry Endpoint ",
              "TextPart": "Greetings from MIC Mail API!"
          }
      ]
  }
}

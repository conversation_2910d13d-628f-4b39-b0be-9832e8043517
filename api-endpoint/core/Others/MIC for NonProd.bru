meta {
  name: MIC for NonProd
  type: http
  seq: 9
}

post {
  url: https://ssoalpha.dvb.corpinter.net/v1/token
  body: formUrlEncoded
  auth: basic
}

headers {
  Content-Type: application/x-www-form-urlencoded
}

auth:basic {
  username: {{tokenmaster_client_id}}
  password: {{tokenmaster_secret}}
}

body:form-urlencoded {
  scope: openid profile email mic:env:prod groups audience:server:client_id:{{tokenmaster_client_id}}
  grant_type: client_credentials
}

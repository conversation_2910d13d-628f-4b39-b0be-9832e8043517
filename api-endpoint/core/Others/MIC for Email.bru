meta {
  name: MIC for Email
  type: http
  seq: 10
}

post {
  url: https://ssoalpha.dvb.corpinter.net/v1/token
  body: formUrlEncoded
  auth: basic
}

params:query {
  : 
}

headers {
  Content-Type: application/x-www-form-urlencoded
}

auth:basic {
  username: {{tokenmaster_client_id}}
  password: {{tokenmaster_secret}}
}

body:form-urlencoded {
  scope: openid profile groups audience:server:client_id:DAIVBADM_MICTM_EMEA_PROD_00968
  grant_type: client_credentials
}

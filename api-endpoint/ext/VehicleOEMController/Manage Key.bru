meta {
  name: Manage Key
  type: http
  seq: 9
}

post {
  url: {{ext_url}}/ext/api/v1/vehicleoem/v1/manageKey
  body: json
  auth: bearer
}

headers {
  x-requestId: aea8777a-8de4-44f6-bc52-ca8e44c4e8a6
  x-fmsId: MBC.CBNG.SIXTH
  x-sbodId: sbod
  x-responseId: dd3b8fa0-4cc9-4ae9-ab81-f6bb96164070
  Content-Type: application/json
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "keyID": "394858302023",
    "action": "TERMINATE",
    "terminationAttestation": "5CFEB534D8F5CEFA6D1B780BCB5CFEB534D95912CEFA6D1B780BCB5CFE",
    "deviceRemoteTerminationRequest": {
      "version": "ECIES_v1",
      "ephemeralPublicKey": "Cwk265HB+sB93ZCVBjxM2",
      "publicKeyHash": "04613197827d91806d630bc4adff44686b012316eb03825f2d6587ffd58d32f4522ada80cc93679e1a316dc0729ebf8172fd41f0c0c1bdda01126f1a6186b2a008",
      "data": "5GRH7/ecb85HFsTalxn3IdeT7ARtfFZn2AuMft1pIkcchjFBLJTGm9qvJtxK/3EdWob+iFS9FwHeKSjq8MxdNgQ1rj4fq6CzQfY8O9"
    },
    "serverRemoteTerminationRequest": "3AEBF536D2F5CEFA6D1C780BCA9CEBF534E90734CEFA2D1B780ACB8EBC",
    "vehicleOEMProprietaryData": "22DZBTV96H1VWW3722DZBTV96H1VWW37"
  }
  
  
  
}

meta {
  name: Receive From Vehicle
  type: http
  seq: 7
}

post {
  url: {{ext_url}}/ext/api/v1/vehicleoem/v1/receivefromvehicle/19UUA765X7A035631
  body: json
  auth: bearer
}

headers {
  x-requestId: aea8777a-8de4-44f6-bc52-ca8e44c4e8a6
  x-fmsId: MBC.CBNG.SIXTH
  x-sbodId: sbod
  x-responseId: dd3b8fa0-4cc9-4ae9-ab81-f6bb96164070
  Content-Type: application/json
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "messagepayload": "d01fe23dc4"
  }
  
}

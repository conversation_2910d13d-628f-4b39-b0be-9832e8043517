auth {
  mode: oauth2
}

auth:oauth2 {
  grant_type: client_credentials
  access_token_url: https://ssoalpha.dvb.corpinter.net/v1/token
  refresh_token_url: 
  client_id: {{tokenmaster_client_id}}
  client_secret: {{tokenmaster_secret}}
  scope: openid profile email mic:env:prod groups audience:server:client_id:{{tokenmaster_client_id}}
  credentials_placement: body
  credentials_id: credentials
  token_placement: header
  token_header_prefix: Bearer
  auto_fetch_token: true
  auto_refresh_token: false
}

script:post-response {
  if(req.getAuthMode() == 'oauth2' && res.body.access_token) {
      bru.setVar('access_token_set_by_collection_script', res.body.access_token);
  }
}

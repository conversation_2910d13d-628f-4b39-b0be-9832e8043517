#!/bin/bash

# Input parameters
CL_NAME=$1
CL_RG=$2
DATADOG_NS=$3
DATADOG_NAME=$4


# Get aks cred and connect to cluster
az aks get-credentials --resource-group $CL_RG --name $CL_NAME
if [ "$?" == 0 ]; then
        echo "=============================================================================================="
        echo "Credentials fetched. Continue..."
        
else
        echo "=============================================================================================="
        echo "Something went WRONG !!! Check Cluster Name and/or Resource group !!! Exiting..."
        echo "=============================================================================================="
        exit 1
fi

# Check if datadog helm deployments exists!
helm ls -n $DATADOG_NS | grep deployed
if [ "$?" == 0 ]; then
        echo "=============================================================================================="
        echo "Datadog helm chart exist. Continue..."
else
        echo "=============================================================================================="
        echo "Datadog deployment does not exist! Exiting script and continuing ..."
        rm -rf /home/<USER>/.kube/config
        echo "=============================================================================================="
        exit 0
fi

# Delete datadog chart
helm delete -n $DATADOG_NS $DATADOG_NAME
if [ "$?" == 0 ]; then
        echo "=============================================================================================="
        echo "Datadog helm chart deleted successfully. Continue..."
        
else
        echo "=============================================================================================="
        echo "Something went WRONG !!! Check datadog helm deployment status in cluster $CL_NAME!!! Exiting..."
        echo "=============================================================================================="
        exit 1
fi

# Delete ingress namespace
kubectl delete ns $DATADOG_NS
if [ "$?" == 0 ]; then
        echo "=============================================================================================="
        echo "Datadog namespace deleted successfully. Continue..."
        
else
        echo "=============================================================================================="
        echo "Something went WRONG !!! Check datadog namespace in cluster $CL_NAME!!! Exiting..."
        echo "=============================================================================================="
        exit 1
fi

# Delete aks config
echo "Deleting AKS config"
rm -rf /home/<USER>/.kube/config
if [ "$?" == 0 ]; then
        echo "=============================================================================================="
        echo "Cluster config deleted successfully. Continue..."
        
else
        echo "=============================================================================================="
        echo "Something went WRONG!!! Exiting..."
        echo "=============================================================================================="
        exit 1
fi

echo "=============================================================================================="

# namespace="datadog"
# echo "Fetching Helm releases in namespace: $namespace"
# releases=$(helm ls -a -n "$namespace" --output json | jq -r '.[] | select(.status == "deployed") | .name')

# if [ -z "$releases" ]; then
#     echo "No deployed releases found in namespace: $namespace"
#     exit 0
# fi

# echo "The following releases will be deleted:"
# echo "$releases"
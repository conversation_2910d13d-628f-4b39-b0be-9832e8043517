#!/bin/bash
# Copyright © [2021] Mercedes-Benz AG. All rights reserved

# Creates a tfvars file from an arbitrary list of input parameters. The number of
# input parameters must be even. The first element is always treated as the variable name
# and the second is treated as variable number. 
# Use for creation of Azure SP credentials.

set -e

if (($# % 2 != 0)) || (($# == 0)); then
  echo "Number of parameters must be even and greater than zero"
  exit 1
fi

VAR_FILE="azure.auto.tfvars"
rm -f $VAR_FILE

while test $# -gt 0; do
  NAME=$1
  shift
  VALUE=$1
  shift
  echo "$NAME = \"$VALUE\"" >> $VAR_FILE
done
#!/bin/bash

# Input parameters
local_rg=$1
local_clustername=$2
local_resource_size=$3
INGRESS_NAMESPACE=$4


# Modified values for prometheus and set resource limits 

if [[ "$local_resource_size" == "prod" ]]; then
echo "used resource size prod (Custom size)"
mod_values='datadog:
    confd:
        nginx_ingress_controller.yaml: |-
            init_config:
            instances:
                - prometheus_url: "http://NGINX_INGRESS_METRICS.'$INGRESS_NAMESPACE'.svc.cluster.local:PORT/metrics"
agents:
    containers:
        agent:
            resources:
                limits:
                    cpu: 300m
                    memory: 600Mi
                requests:
                    cpu: 150m
                    memory: 300Mi
        traceAgent: 
            resources:
                limits: 
                    cpu: 10m
                    memory: 200Mi
                requests:
                    cpu: 5m
                    memory: 100Mi
        processAgent:
            resources:
                limits: 
                    cpu: 30m
                    memory: 400Mi
                requests:
                    cpu: 15m
                    memory: 200Mi
        systemProbe:
            resources:
                limits:
                    cpu: 40m
                    memory: 1600Mi
                requests:
                    cpu: 20m
                    memory: 800Mi
'
elif [[ "$local_resource_size" == "prod-small" ]]; then
echo "used resource size prod-small (~2/3 of prod size)"

    mod_values='datadog:
    confd:
        nginx_ingress_controller.yaml: |-
            init_config:
            instances:
                - prometheus_url: "http://NGINX_INGRESS_METRICS.'$INGRESS_NAMESPACE'.svc.cluster.local:PORT/metrics"
agents:
    containers:
        agent:
            resources:
                limits:
                    cpu: 200m
                    memory: 400Mi
                requests:
                    cpu: 100m
                    memory: 200Mi
        traceAgent:
            resources:
                limits:
                    cpu: 6m
                    memory: 125Mi
                requests:
                    cpu: 3m
                    memory: 60Mi
        processAgent:
            resources:
                limits:
                    cpu: 20m
                    memory: 275Mi
                requests:
                    cpu: 10m
                    memory: 125Mi
        systemProbe:
            resources:
                limits:
                    cpu: 30m
                    memory: 1000Mi
                requests:
                    cpu: 15m
                    memory: 500Mi
'
elif [[ "$local_resource_size" == "nonprod" ]]; then
echo "used resource size nonprod (~1/3 of prod size)"

    mod_values='datadog:
    confd:
        nginx_ingress_controller.yaml: |-
            init_config:
            instances:
                - prometheus_url: "http://NGINX_INGRESS_METRICS.'$INGRESS_NAMESPACE'.svc.cluster.local:PORT/metrics"
agents:
    containers:
        agent:
            resources:
                limits:
                    cpu: 100m
                    memory: 200Mi
                requests:
                    cpu: 50m
                    memory: 100Mi
        traceAgent:
            resources:
                limits:
                    cpu: 4m
                    memory: 60Mi
                requests:
                    cpu: 2m
                    memory: 30Mi
        processAgent:
            resources:
                limits:
                    cpu: 10m
                    memory: 125Mi
                requests:
                    cpu: 5m
                    memory: 60Mi
        systemProbe:
            resources:
                limits:
                    cpu: 20m
                    memory: 500Mi
                requests:
                    cpu: 10m
                    memory: 250Mi
'
fi


if [[ -z "$mod_values" ]]; then
    echo "No valid value file found, please provide the a local_resource_size"
    exit 1
fi

echo "$mod_values" > mod_values.yml

# Find region
local_region=$(az aks show --name $local_clustername --resource-group $local_rg --query location -o json | tr -d '"')
if [[ -z "$local_region" ]]; then
    echo "Could not find region of Cluster"
    exit 1
fi

# Find geo location
if [[ "$local_region" == *"europe"* ]]; then
    local_geo="ece"
elif [[ "$local_region" == *"china"* ]]; then
    local_geo="china"
elif [[ "$local_region" == *"us"* ]]; then
    local_geo="amap"
fi

# Set Clustername as fully qualified infrastructure name (fqin)
local_fqin=$(echo $local_clustername | tr ' ' '\n' )



# Find additional tags
tag_project=$(az aks show --name $local_clustername --resource-group $local_rg -o json --query tags.mic_project | tr -d '"')
tag_department=$(az aks show --name $local_clustername --resource-group $local_rg -o json --query tags.mic_department | tr -d '"')
tag_stage=$(az aks show --name $local_clustername --resource-group $local_rg -o json --query tags.mic_stage | tr -d '"')

# Find stage
# local_stage=$(az aks show --name local_clustername --resource-group local_rg -o json --query tags.mic_stage | tr -d '"') # allow to use label to name stages
if [[ -z  "$tag_stage" ]]; then
    if [[ "$local_clustername" == *"dev"* ]]; then
        local_stage="dev"
    elif [[ "$local_clustername" == "nonprod"* ]]; then
        local_stage="nonprod"
    elif [[ "$local_clustername" == "prod"* ]]; then
        local_stage="prod"
    fi
else
    if [[ "$tag_stage" == *"dev"* ]]; then
        local_stage="dev"
    elif [[ "$tag_stage" == *"nonprod"* ]]; then
        local_stage="nonprod"
    elif [[ "$tag_stage" == *"prod"* ]]; then
        local_stage="prod"
    fi
fi

if [[ -z "$local_stage" ]]; then
    echo "local_stage has no value, please add the tag mic_stage to the k8s resource in azure and set the value to test or prod"
    exit 1
fi

# Get Ingress metric svc which has been fetched from the step before
az aks get-credentials --resource-group $local_rg --name $local_clustername
# ingress_metric_svc=$(kubectl get svc -A -l $INGRESS_LABEL -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | grep metrics | xargs echo -n)
ingress_metric_svc=$(kubectl get svc -n $INGRESS_NAMESPACE -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | grep metrics)
ingress_metric_port=$(kubectl get svc -n $INGRESS_NAMESPACE $ingress_metric_svc -o json | jq ".spec.ports[].port")

echo "found nginx ingress metric service: $ingress_metric_svc"
echo "found nginx ingress metric port: $ingress_metric_port"

if [[ -z "$ingress_metric_svc" ]]; then
    echo "No nginx-ingress-metric service found"
    echo "Use default nginx metric service name"
    ingress_metric_svc="ingress-nginx-controller-metrics"
fi
#sed -i s/NGINX_INGRESS_METRICS/$ingress_metric_svc/g mod_values.yml
sed -i '' -e "s/NGINX_INGRESS_METRICS/$ingress_metric_svc/g" mod_values.yml
sed -i '' -e "s/PORT/$ingress_metric_port/g" mod_values.yml


# Format clustername for datadog
datadog_clustername=$(echo $local_clustername | tr '_' '-' )

echo "region: $local_region"
echo "geo: $local_geo"
echo "stage: $local_stage"
echo "fqin: $local_clustername"
echo "project: $tag_project"
echo "department: $tag_department"
echo "nginx metric svc: $ingress_metric_svc"
echo "formatted clustername: $datadog_clustername"

echo "used mod-values:"
echo "$(cat mod_values.yml)"

# Check if necessary variables are set
if [[ -z local_geo || -z local_region || -z local_stage || -z local_fqin ]]; then
    echo "Please make sure all necessary tags are provided https://gsep.daimler.com/confluence/display/MIC/Step+1%3A+Datadog+Deployment+daemonset"
    exit 1
fi 

echo "##vso[task.setvariable variable=regionTag]$local_region"
echo "##vso[task.setvariable variable=geoTag]$local_geo"
echo "##vso[task.setvariable variable=stageTag]$local_stage"
echo "##vso[task.setvariable variable=projectTag]$tag_project"
echo "##vso[task.setvariable variable=departmentTag]$tag_department"
echo "##vso[task.setvariable variable=nginxMetrics]$ingress_metric_svc"
echo "##vso[task.setvariable variable=nginxMetrics]$ingress_metric_svc"
echo "##vso[task.setvariable variable=datadogClustername]$datadog_clustername"
echo "##vso[task.setvariable variable=datadogDepartment]$tag_department"
echo "##vso[task.setvariable variable=datadogProject]$tag_project"


# Delete aks config
echo "======================================"
echo "Deleting temp AKS config"
rm -rf /home/<USER>/.kube/config

jq '.info += {
               "title": "Server Based Owner Device (SBOD)",
               "x-nameSlug": "sbod",
               "description": "Server Based Owner Device (SBOD) is the future of digital key",
               "version": "v0.1",
               "x-region": "EMEA",
               "x-state": "IN_DEVELOPMENT",
               "x-visibility": "INTERNAL",
               "x-applicationId": "2b5927d7-4ee9-4ad5-85f0-e65ce38ac6a0",
               "x-actions": {
                 "tryItOut": {
                   "enabled": true
                 }
               },
               "x-processors": {
                 "scopeGeneration": {
                   "enabled": true
                 }
               }
             }' sbod.json > tmp.json && mv tmp.json sbod.json

jq 'del (.servers) + {
          "servers": [
            {
              "url": "http://localhost:xxxx/core",
              "description": "Generated server url"
            },
            {

            "url": "https://sbod-nonprod.query.api.dvb.corpinter.net",
            "description": "SBOD NONPROD server"
            }
          ]
        }' sbod.json > tmp.json && mv tmp.json sbod.json
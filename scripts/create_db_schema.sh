HOST_NAME=$1
DB_ADMIN_USER=$2
DB_NAME=$3
NEW_SCHEMA=$4
WORKLOAD_IDENTITY_OID=$5

export PGHOST=$HOST_NAME
export PGPORT=5432
export PGUSER=$DB_ADMIN_USER
export PGPASSWORD="$(az account get-access-token --resource-type oss-rdbms --query accessToken --output tsv)"

psql -d postgres \
     -c "SELECT * FROM pg_catalog.pgaadauth_create_principal_with_oid( \
          '$NEW_SCHEMA', '$WORKLOAD_IDENTITY_OID', 'service', false, false);"

psql -d $DB_NAME \
     -c " CREATE SCHEMA IF NOT EXISTS $NEW_SCHEMA; \
          GRANT CREATE, USAGE ON SCHEMA $NEW_SCHEMA TO $NEW_SCHEMA;  \
          ALTER ROLE $NEW_SCHEMA SET search_path=$NEW_SCHEMA;  \
          GRANT $NEW_SCHEMA to $DB_ADMIN_USER; \
          ALTER DEFAULT PRIVILEGES IN SCHEMA $NEW_SCHEMA GRANT ALL ON TABLES TO $NEW_SCHEMA;  \
          ALTER DEFAULT PRIVILEGES IN SCHEMA $NEW_SCHEMA GRANT ALL ON SEQUENCES TO $NEW_SCHEMA;"

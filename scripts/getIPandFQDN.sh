#!/bin/bash

# Input parameters
CL1=$1

# Allow for defining task variable output parameter names
# in case we have to retrieve multiple public IP´s
output_ip_addr=${3:-pubIP}
output_cluster_rg_name=${4:-ClusterRG}
output_cluster_fqdn=${5:-ClusterFQDN}

# az resource list --query "[?name=='$CL1'].{rg:resourceGroup}" --output tsv
CLRG1="$(az resource list \
            --query "[?name=='$CL1'].{rg:resourceGroup}" \
            --output tsv)"

echo "=========================================================="
echo "CLRG1: $CLRG1."
if [ -z "$CLRG1" ]; then
      echo "Could not get RG for cluster $CL1."
      echo "ClusterRG: $ClusterRG."
      echo "output_cluster_fqdn: $output_cluster_fqdn."
      echo "=========================================================="
      exit 1
else
      echo "Found ClusterRG: $CLRG1. Exporting..."
      echo "=========================================================="
      echo "##vso[task.setvariable variable=${output_cluster_rg_name}]$CLRG1"
fi

CLFQDN1="$(az aks show \
            -g $CLRG1 \
            -n $CL1 \
            | jq '.azurePortalFqdn' | xargs)"

if [ -z "$CLFQDN1" ]; then
      echo "Could not get FQDN for cluster $CL1."
      echo "=========================================================="
      exit 1
else
      echo "Found ClusterFQDN: $CLFQDN1. Exporting..."
      echo "=========================================================="
      echo "##vso[task.setvariable variable=${output_cluster_fqdn}]$CLFQDN1"
fi

# Get IP address using the original method
IP1_ID="$(az network public-ip list \
            --query "[?name=='"$CL1"_IP_IN'].{id:id}" \
            --output tsv)"

ADDR_IP1="$(az network public-ip show \
            --ids $IP1_ID \
            --query "{ip:ipAddress}" \
            --output tsv)"

# Get IP address using the new specific method
SBOD_IP="$(az network public-ip show \
            --resource-group Network-SBOD-Services-PublicIPs \
            --name sbod_ece_1_v1_weu_IP_IN \
            --query "ipAddress" \
            --output tsv)"

if [ -z "$ADDR_IP1" ]; then
      echo "Could not get Public IP for IP_ID: $IP1_ID."
      echo "=========================================================="
      exit 1
else
      echo "Found publicIP: $ADDR_IP1. Exporting..."
      echo "=========================================================="
      # echo "##vso[task.setvariable variable=${output_ip_addr}]$ADDR_IP1"
fi

if [ -z "$SBOD_IP" ]; then
      echo "Could not get SBOD Public IP."
      echo "=========================================================="
else
      echo "Found SBOD publicIP: $SBOD_IP"
      echo "=========================================================="
       echo "##vso[task.setvariable variable=${output_ip_addr}]$SBOD_IP"
fi

echo "SUMMARY INFO:"
echo "Found SBOD publicIP: $SBOD_IP"
echo "Found ClusterRG: $CLRG1"
echo "Found ClusterFQDN: $CLFQDN1"
echo "=========================================================="
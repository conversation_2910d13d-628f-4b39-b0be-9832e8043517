#!/bin/bash

#################################
# Define variables from arguments
#################################
devopsguard_product_name="$1"
devopsguard_apikey="$2"
devopsguard_authentication="$3"
sechub_url="$4"
sechub_project_name="$5"
sechub_user="$6"
sechub_api_token="$7"
#################################

# Function to bring false positive findings from DevOpsGuard
function get_fp_from_devopsguard(){
	echo "[**] Getting false positives findings from DevOpsGuard..."
	raw_fp=$(curl -Ss --fail-with-body --location "https://devopsguard.i.mercedes-benz.com:8888/api-rest/product/${devopsguard_product_name}/sechub/false_positives" \
	--header "x-api-key: ${devopsguard_apikey}"  --user "${devopsguard_authentication}") exitCode=$?

	# curl return '22' for any HTTP error that is 400 or above
	# If DefectDojo curl request fails, show body error from DefectDojo API, advice that the sync is not needed and exit.
	if [ ${exitCode} -eq 22 ]; then
		echo $raw_fp
		echo "Nothing to sync. Exiting..."
		exit 0
	fi

	echo "[**] Finished getting false positives findings from DevOpsGuard..."
}

# Function to send false positives to SecHub
function send_fp_finding_to_sechub() {
	echo "[**] Sending false positive finding to SecHub..."

	curl --fail-with-body "${1}/api/project/${2}/false-positives" -i -u "${3}:${4}" -X PUT \
	--header "Content-Type: application/json;charset=UTF-8" \
	--data "{\"apiVersion\":\"1.0\",\"type\":\"falsePositiveJobDataList\",\"jobData\":[{\"jobUUID\":\"${5}\",\"findingId\":${6},\"comment\":\"${7}\"}]}"
}

# Function to patch false positive findings in DevOpsGuard with the SecHub flag tag
function patch_fp_in_devopsguard(){
	echo "[**] Marking the finding ${1} as synchronized with SecHub, in DevOpsGuard..."
	raw_fp=$(curl -Ss --fail-with-body --location --request PATCH "https://devopsguard.i.mercedes-benz.com:8888/api-rest/finding/${1}/sechub/tag" \
	--header "x-api-key: ${devopsguard_apikey}"  --user "${devopsguard_authentication}") exitCode=$?

	# curl return '22' for any HTTP error that is 400 or above
	# If DefectDojo curl request fails, show body error from DefectDojo API.
	if [ ${exitCode} -eq 22 ]; then
		echo $raw_fp
	fi
}

#######################################
# Main run
#######################################

get_fp_from_devopsguard

# Loop the JSON response from above request
# Take the needed info from each false positive finding
echo $raw_fp | jq -c .[] | while read -r i; do
	IN=$(echo "$i" | jq -r .vuln_id_from_tool) # FindingID#jobUUID
	arrIN=(${IN//#/ })	# Split the string and take FindingID - jobUUID
	sechub_findingid=${arrIN[0]}
	jobuuid=${arrIN[1]}
	comment=$(echo "$i" | jq -r .notes[-1].entry) # Take false positive closing comment from DevOpsGuard

	dog_findingid=$(echo "$i" | jq -r .id) # Take DevOpsGuard finding id

	# Per each finding, send the request to SecHub with the required parameters
	if send_fp_finding_to_sechub "$sechub_url" "$sechub_project_name" "$sechub_user" "$sechub_api_token" "$jobuuid" "$sechub_findingid" "$comment" ; then
	    patch_fp_in_devopsguard "$dog_findingid"
	else
	    echo "Failed sync with SecHub for DOG finding_id $dog_findingid "
	fi

done

environment: dev

serviceAccount:
  name: sbod-core-dev-workload-identity

image:
  repository: sbodacrdev.azurecr.io/sbod-core

minReplicas: 1
maxReplicas: 3

env:
  - name: SPRING_PROFILES_ACTIVE
    value: dev
  - name: TOKENMASTER_SCOPE
    value: openid profile email mic:env:prod groups audience:server:client_id:DAIVBADM_MICTM_EMEA_NONPROD_00079
  - name: TLS_CERT
    valueFrom:
      secretKeyRef:
        name: mic-gateway-clntcert
        key: tls.crt
  - name: TLS_KEY
    valueFrom:
      secretKeyRef:
        name: mic-gateway-clntcert
        key: tls.key

resources:
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  requests:
    cpu: 100m
    memory: 1Gi

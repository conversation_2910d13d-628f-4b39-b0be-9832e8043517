# This values.yaml file contains the values needed to enable HA mode.
# Usage:
#   helm install -f values.yaml -f values-ha.yaml

enablePodAntiAffinity: true
enablePodDisruptionBudget: true

# nodeAffinity:

resources: &ha_resources
  cpu: &ha_resources_cpu
    limit: ""
    request: 100m
  memory:
    limit: 250Mi
    request: 50Mi


# tap configuration
tap:
  replicas: 3
  resources: *ha_resources

# web configuration
dashboard:
  resources: *ha_resources

# prometheus configuration
prometheus:
  resources:
    cpu:
      limit: ""
      request: 300m
    memory:
      limit: 8192Mi
      request: 300Mi

global:
  # -----
  # - required parameters
  region: "emea" # emea, amap or cn
  stage: "nonprod" # nonprod, prod
  microServiceName: "sbod-nonprod"
  microServiceFullName: "sbod-nonprod" # the full name of your microservice, e.g. certificate-exchange-protocol

  clientid: ""
  clientsecret: ""

  upstreams:
    - sourcePath: /ext
      upstream: http://sbod-ext.sbod-nonprod.svc:8051
      requestTimeout: "60s"
      responseTimeout: "60s"
      resources:
        - uri: /ext/api/v1/*
          require-any-scope: false
          require-any-group: true
          methods:
            - GET
            - POST
            - PUT
          groups:
            - DAIVBADM.SBODEXT_ROLE_FULLACCESS_NONPROD
          white-listed: false
        - uri: /ext/vehicleoem/v1/*
          require-any-scope: false
          require-any-group: true
          methods:
            - GET
            - POST
            - PUT
          groups:
            - DAIVBADM.SBODEXT_ROLE_FULLACCESS_NONPROD
          white-listed: false
        - uri: /ext/swagger-ui/*
          require-any-scope: false
          require-any-group: false
        - uri: /ext/v3/api-docs/*
          require-any-scope: false
          require-any-group: false
      allowUserType:
        - internal
        - external_partner
        - technical
    - sourcePath: /core
      upstream: http://sbod-core.sbod-nonprod.svc:8050
      requestTimeout: "60s"
      responseTimeout: "60s"
      resources:
        - uri: /core/api/v1/*
          require-any-scope: false
          require-any-group: true
          methods:
            - GET
            - POST
            - PUT
          groups:
            - DAIVBADM.SBODCORE_ROLE_FULLACCESS_NONPROD
          white-listed: false
        - uri: /core/job/*
          require-any-scope: false
          require-any-group: true
          methods:
            - GET
            - POST
            - PUT
          groups:
            - DAIVBADM.SBODCORE_ROLE_FULLACCESS_NONPROD
          white-listed: false
        - uri: /core/test/*
          require-any-scope: false
          require-any-group: true
          methods:
            - GET
            - POST
            - PUT
          groups:
            - DAIVBADM.SBODCORE_ROLE_FULLACCESS_NONPROD
          white-listed: false
        - uri: /core/swagger-ui/*
          require-any-scope: false
          require-any-group: false
        - uri: /core/v3/api-docs/*
          require-any-scope: false
          require-any-group: false
      allowUserType:
        - internal
        - external_partner
        - technical
    - sourcePath: /status
      upstream: http://sbod-core.sbod-nonprod.svc:8050/core/status
      requestTimeout: "60s"
      responseTimeout: "60s"
      resources:
        - uri: /status
          require-any-scope: false
          require-any-group: false
      allowUserType:
        - internal
        - external_partner
        - technical

  hosts:
    - hostname: "sbod-nonprod.query.api.dvb.corpinter.net"
      ingressName: "nginx"
      syncServiceInConsul: false
      ssl:
        enabled: true
        useCertMgr: true # true if you use the cert manager, otherwise set this to false and define crt and key
        crt: ""
        key: ""
      tlsAuth:
        enabled: true # set this to true to enable client certificate authentication
        trustedCertificationAuthorities:
          - service01
          - service02
          - user01
          - user02
      ingressAnnotations:
        nginx.ingress.kubernetes.io/enable-cors: "true"
        nginx.ingress.kubernetes.io/cors-allow-origin: "https://portal.mbos.mercedes-benz.com"
        nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
        nginx.ingress.kubernetes.io/cors-allow-headers: "DNT, X-CustomHeader, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Authorization, X-XSRF-Token, X-CSRF-Token, x-requestid, x-fmsid"
      # some room for extra ingress annotations

  basepath: "/" # defines the basepath to listen on

  # -----
  # - optional parameters
  optional:
    # if deployed to the Shared Cluster Platform, this needs to be set to true
    micSharedClusterPlatform: false

    # if set to true, then the gateway will be deployed as a daemonset, rather then a regular replicaset based
    # deployment. a daemonset brings a more robust predection in means of resource utilization for high-load scenarios
    # the helper containers are not impacted. they will still be deployed as a replicaset (fixed replica amount set to 1)
    daemonset: false

    # disableHeaderKeyNormalizing: true   # disables the canonicalization of response and request header keys, default false

    aksversion: "1.28.3"

    dockerPullID: "" # if you want to specify a manual docker pull secret, put the ID of the user that can images from the IAMS registry in here
    dockerPullSecret: "" # if you want to specify a manual docker pull secret, put the secret of the user that can images from the IAMS registry in here

    loglevel: "debug"

    replicaCount: 1 # amount of pod replicas.

    # add additional headers to the upstream proxy call
    # example:
    # addHeaders:
    #   Authorization: "Basic abcdef"
    #   X-CustomHeader: "some value"
    addHeaders: []

    serviceAccount:
      create: false
      name: ""

    namespaceOverride: ""
    nameOverride: ""
    fullnameOverride: ""

    podSecurityContext:
      {}
      # fsGroup: 2000

    securityContext:
      {}
      # capabilities:
      #   drop:
      #   - ALL
      # readOnlyRootFilesystem: true
      # runAsNonRoot: true
      # runAsUser: 1000

    nodeSelector: {}
    tolerations: []
    affinity: {}

    upstreamTimeout: ***********
    upstreamKeepalive: ***********

    # affinityProfile has two predefined affinity modes
    #   1. gatewayAntiAffinity
    #      This profile ensures that all gateway replicas are scheduled on different nodes as much as possible
    #   2. podAffinity
    #      This option ensures that the gateway pods are scheduled on the same node as a specified pod
    #      The podName attribute needs to be set according to the pod you want to schedule your gateway alongside with

    affinityProfile:
      profile: "gatewayAntiAffinity"
      #labelSelector: ""                          # needs to be set in case profile is set to podAffinity
      #  key: ""
      #  value: ""

    # If the predefined affinityProfiles do not match your deployment scenario, you can define a custom affinity here
    #affinity:
    #  podAntiAffinity:
    #    preferredDuringSchedulingIgnoredDuringExecution:
    #    - weight: 100
    #      podAffinityTerm:
    #        labelSelector:
    #          matchExpressions:
    #          - key: security
    #            operator: In
    #            values:
    #            - S1
    #        topologyKey: topology.kubernetes.io/zone

####
# certmgr configuration
###
crtmgr:
  enabled: true
  optional:
    {}
    # imagetag: "latest"
    # k8sresources: {}
    # clientId: ""
    # clientSecret: ""
    # issuerUrl: ""

####
# gateway configuration
###
gateway:
  enabled: true
  optional:
    disableCertificateCnCheck: true
    # passthrough: false
    # imagetag: "latest"
    # enableRefreshTokens: false
    enableTokenHeader: false # you can set to false if you don't use X-Auth-Token header in your service
    enableAuthorizationHeader: true
    # enableSmuniversalid: false
    k8sresources:
      requests:
        cpu: 20m
        memory: 40Mi
      limits:
        cpu: 100m
        memory: 128Mi
    requestedScopes:
      - "openid" # standard OIDC scope
      - "profile" # standard OIDC scope
      - "email" # standard OIDC scope
    #   - "groups"  # standard OIDC scope
    #   - "offline_access"
    #    corsOrigins:
    #      - "http://localhost:3000"
    #      - "https://extendsclass.com"
    #    corsMethods:
    #      - "GET"
    #      - "PUT"
    #      - "POST"
    #      - "OPTIONS"
    #      - "HEAD"
    #      - "DELETE"
    #      - "PATCH"
    #    corsHeaders:
    #      - "authorization"
    #      - "content-type"
    #      - "access-control-allow-origin"
    datadog:
      enabled: false
      # <serviceName> (optional), allows to overwrite the datadog service name
      # serviceName: <full-service-name-tgw>

      # <tags> (optional), allows setting additional datadog tags.
      # tags:
      #   key: "value"
      #   abc: "xyz"
      #   region: "westeurope"
    ####
    # siem configuration
    ###
    siem:
      enabled: false
      # url: ""
      # apiKey: ""
      # source: "application-name"
      # index: "server-side-index-name"
      # bufferSize: 10000
      # concurrency: 5
      # eventBatchNo: 100
      # eventBatchBytes: 4096
      # eventFlushIntervalSeconds: 10
      # watchDogIntervalMillis: 200
      # watchDogBufferSize: 50
      # watchDogEventDropsPercentage: 10
      # statisticsIntervalSeconds: 60
      # clientTimeoutSeconds: 10
      # clientRetryMax: 5
      # clientRetryHttpCodes: 408,504
      # clientRetryIntervalSeconds: 10

    # k8sresources: {}

####
# authcache configuration
###
authcache:
  enabled: false
  optional:
    # imagetag: "latest"
    # redisEnabled: false
    # redisUrl: ""
    # redisPassword: ""
    # redisExpirytimeseconds: ""
    # redisNamespace: ""
    # redisUsetls: ""
    # audience: ""
    # userMappings: []
    # - from: ""
    #   to: ""
    # k8sresources: {}
    datadog:
      enabled: false
      # <serviceName> (optional), allows to overwrite the datadog service name
      # serviceName: <full-service-name-auth-cache>

      # <tags> (optional), allows setting additional datadog tags.
      # tags:
      #   key: "value"
      #   abc: "xyz"
      #   region: "westeurope"

####
# authapp configuration
###
authapp:
  enabled: false
  optional:
    {}
    # imagetag: "latest"
    # authurl: "/authenticate"
    # k8sresources: {}

####
# azsproxy configuration
###
azsproxy:
  enabled: false
  optional:
    tlsAuth:
      enable: false
    # cert: "cert"
    # key: "key"
    # mode: "release"
    # imagetag: "latest"
    # upstream: "upstream.host.com"
    # logLevel: "info"
    # k8sresources: {}
    azs: {}
    #   baseUrl: "https://azs-dev.mmcplatform-test.app.corpintra.net"
    #   username: ""
    #   password: ""
    #   timeoutInSec: 10
    #   pepVersion: 30
    #   whitelistedCallerId: "MBC_azs_00"

####
# healthshipper configuration
###
healthshipper:
  enabled: true
  micSharedClusterPlatform: false
  optional:
    {}
    # imagetag: ""
    # datadog:
    #   tags: []
  # k8sresources: {}

####
# web application firewall configuration
###
waf:
  enabled: true
  optional:
    logLevel: "debug" #logLevel must be "info" or "debug"
    k8sresources:
      requests:
        cpu: 20m
        memory: 128Mi
      limits:
        cpu: 150m
        memory: 300Mi

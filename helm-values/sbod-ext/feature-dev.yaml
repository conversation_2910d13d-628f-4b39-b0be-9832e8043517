environment: "dev"

serviceAccount:
  name: sbod-ext-dev-workload-identity

env:
  - name: SPRING_PROFILES_ACTIVE
    value: feature
  - name: TOKENMASTER_CLIENT_ID
    value: TESTING
  - name: TLS_CERT
    valueFrom:
      secretKeyRef:
        name: mic-gateway-clntcert
        key: tls.crt
  - name: TLS_KEY
    valueFrom:
      secretKeyRef:
        name: mic-gateway-clntcert
        key: tls.key

image:
  repository: sbodacrdev.azurecr.io/sbod-ext

minReplicas: 1
maxReplicas: 3

resources:
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  requests:
    cpu: 100m
    memory: 1Gi

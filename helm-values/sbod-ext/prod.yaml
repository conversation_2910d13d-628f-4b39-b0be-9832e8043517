environment: "prod"

serviceAccount:
  name: sbod-ext-nonprod-workload-identity

env:
  - name: SPRING_PROFILES_ACTIVE
    value: prod
  - name: TOKENMASTER_SCOPE
    value: openid profile email mic:env:prod groups audience:server:client_id:DAIVBADM_MICTM_EMEA_NONPROD_00079
  - name: TOKENMASTER_CLIENT_ID
    value: TESTING
  - name: TLS_CERT
    valueFrom:
      secretKeyRef:
        name: mic-gateway-clntcert
        key: tls.crt
  - name: TLS_KEY
    valueFrom:
      secretKeyRef:
        name: mic-gateway-clntcert
        key: tls.key

minReplicas: 2
maxReplicas: 5

resources:
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  requests:
    cpu: 100m
    memory: 1Gi

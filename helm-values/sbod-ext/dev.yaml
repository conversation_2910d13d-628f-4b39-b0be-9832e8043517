environment: "dev"

image:
  repository: sbodacrdev.azurecr.io/sbod-ext

minReplicas: 1
maxReplicas: 3

serviceAccount:
  name: sbod-ext-dev-workload-identity

env:
  - name: SPRING_PROFILES_ACTIVE
    value: dev
  - name: TOKENMASTER_CLIENT_ID
    value: TESTING
  - name: TLS_CERT
    valueFrom:
      secretKeyRef:
        name: mic-gateway-clntcert
        key: tls.crt
  - name: TLS_KEY
    valueFrom:
      secretKeyRef:
        name: mic-gateway-clntcert
        key: tls.key

resources:
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  requests:
    cpu: 100m
    memory: 1Gi

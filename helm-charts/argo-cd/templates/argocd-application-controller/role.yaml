apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "argo-cd.controller.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - secrets
  - configmaps
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - argoproj.io
  resources:
  - applications
  - appprojects
  verbs:
  - create
  - get
  - list
  - watch
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - list
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - get
  - list
  - watch
{{- if and (not .Values.createClusterRoles) .Values.controller.dynamicClusterDistribution }}
- apiGroups:
  - ""
  resources:
  - configmaps
  resourceNames:
  - argocd-app-controller-shard-cm
  verbs:
  - get
  - list
  - watch
  - create
  - update
{{- end }}

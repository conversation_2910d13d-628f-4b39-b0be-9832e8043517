apiVersion: v1
kind: Service
metadata:
  name: {{ template "argo-cd.server.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
    {{- with .Values.server.service.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.server.service.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.server.service.type }}
  {{- include "argo-cd.dualStack" . | indent 2 }}
  {{- with .Values.server.service.externalIPs }}
  externalIPs: {{ . }}
  {{- end }}
  {{- if or (eq .Values.server.service.type "LoadBalancer") (eq .Values.server.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.server.service.externalTrafficPolicy }}
  {{- end }}
  {{- if eq .Values.server.service.type "LoadBalancer" }}
  {{- with .Values.server.service.loadBalancerClass }}
  loadBalancerClass: {{ . }}
  {{- end }}
  {{- with .Values.server.service.loadBalancerIP }}
  loadBalancerIP: {{ . }}
  {{- end }}
  {{- with .Values.server.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
  sessionAffinity: {{ .Values.server.service.sessionAffinity }}
  ports:
  - name: {{ .Values.server.service.servicePortHttpName }}
    protocol: TCP
    port: {{ .Values.server.service.servicePortHttp }}
    targetPort: {{ .Values.server.containerPorts.server }}
    {{- if eq .Values.server.service.type "NodePort" }}
    nodePort: {{ .Values.server.service.nodePortHttp }}
    {{- end }}
  - name: {{ .Values.server.service.servicePortHttpsName }}
    protocol: TCP
    port: {{ .Values.server.service.servicePortHttps }}
    targetPort: {{ .Values.server.containerPorts.server }}
    {{- if eq .Values.server.service.type "NodePort" }}
    nodePort: {{ .Values.server.service.nodePortHttps }}
    {{- end }}
    {{- with .Values.server.service.servicePortHttpsAppProtocol }}
    appProtocol: {{ . }}
    {{- end }}
  selector:
    {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.server.name) | nindent 4 }}


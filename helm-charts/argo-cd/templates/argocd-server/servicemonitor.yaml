{{- if and (.Capabilities.APIVersions.Has "monitoring.coreos.com/v1") .Values.server.metrics.enabled .Values.server.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "argo-cd.server.fullname" . }}
  namespace: {{ default (include  "argo-cd.namespace" .) .Values.server.metrics.serviceMonitor.namespace | quote }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
    {{- with .Values.server.metrics.serviceMonitor.selector }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.server.metrics.serviceMonitor.additionalLabels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.server.metrics.serviceMonitor.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  endpoints:
    - port: {{ .Values.server.metrics.service.portName }}
      {{- with .Values.server.metrics.serviceMonitor.interval }}
      interval: {{ . }}
      {{- end }}
      {{- with .Values.server.metrics.serviceMonitor.scrapeTimeout }}
      scrapeTimeout: {{ . }}
      {{- end }}
      path: /metrics
      {{- with .Values.server.metrics.serviceMonitor.relabelings }}
      relabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.metrics.serviceMonitor.metricRelabelings }}
      metricRelabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      honorLabels: {{ .Values.server.metrics.serviceMonitor.honorLabels }}
      {{- with .Values.server.metrics.serviceMonitor.scheme }}
      scheme: {{ . }}
      {{- end }}
      {{- with .Values.server.metrics.serviceMonitor.tlsConfig }}
      tlsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  namespaceSelector:
    matchNames:
      - {{ include "argo-cd.namespace" . }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "component" .Values.server.name "name" (printf "%s-metrics" .Values.server.name)) | nindent 6 }}
{{- end }}

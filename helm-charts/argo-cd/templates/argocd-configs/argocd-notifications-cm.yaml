{{- if and .Values.notifications.enabled .Values.notifications.cm.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
data:
  context: |
    argocdUrl: {{ .Values.notifications.argocdUrl | default (printf "https://%s" .Values.global.domain) }}
    {{- with .Values.notifications.context }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.notifications.notifiers }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
  {{- with .Values.notifications.subscriptions }}
  subscriptions: |
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.notifications.templates }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
  {{- with .Values.notifications.triggers }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}

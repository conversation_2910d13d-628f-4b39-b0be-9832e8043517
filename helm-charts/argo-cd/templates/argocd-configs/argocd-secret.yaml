{{- if .Values.configs.secret.createSecret }}
apiVersion: v1
kind: Secret
metadata:
  name: argocd-secret
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" "secret") | nindent 4 }}
    {{- with .Values.configs.secret.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.configs.secret.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: Opaque
{{- if or .Values.configs.secret.githubSecret (or .Values.configs.secret.gitlabSecret .Values.configs.secret.bitbucketUUID .Values.configs.secret.bitbucketServerSecret .Values.configs.secret.gogsSecret (and .Values.configs.secret.azureDevops.username .Values.configs.secret.azureDevops.password) .Values.configs.secret.argocdServerAdminPassword .Values.configs.secret.extra) }}
# Setting a blank data again will wipe admin password/key/cert
data:
  {{- with .Values.configs.secret.githubSecret }}
  webhook.github.secret: {{ . | b64enc }}
  {{- end }}
  {{- with .Values.configs.secret.gitlabSecret }}
  webhook.gitlab.secret: {{ . | b64enc }}
  {{- end }}
  {{- with .Values.configs.secret.bitbucketServerSecret }}
  webhook.bitbucketserver.secret: {{ . | b64enc }}
  {{- end }}
  {{- with .Values.configs.secret.bitbucketUUID }}
  webhook.bitbucket.uuid: {{ . | b64enc }}
  {{- end }}
  {{- with .Values.configs.secret.gogsSecret }}
  webhook.gogs.secret: {{ . | b64enc }}
  {{- end }}
  {{- if and .Values.configs.secret.azureDevops.username .Values.configs.secret.azureDevops.password }}
  webhook.azuredevops.username: {{ .Values.configs.secret.azureDevops.username | b64enc }}
  webhook.azuredevops.password: {{ .Values.configs.secret.azureDevops.password | b64enc }}
  {{- end }}
  {{- if .Values.configs.secret.argocdServerAdminPassword }}
  admin.password: {{ .Values.configs.secret.argocdServerAdminPassword | b64enc }}
  admin.passwordMtime: {{ default (dateInZone "2006-01-02T15:04:05Z" (now) "UTC") .Values.configs.secret.argocdServerAdminPasswordMtime | b64enc }}
  {{- end }}
  {{- range $key, $value := .Values.configs.secret.extra }}
  {{ $key }}: {{ $value | b64enc }}
  {{- end }}
{{- end }}
{{- end }}

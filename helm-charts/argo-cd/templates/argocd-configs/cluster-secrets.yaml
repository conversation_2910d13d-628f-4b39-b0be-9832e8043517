{{- range $cluster_key, $cluster_value := .Values.configs.clusterCredentials }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "argo-cd.name" $ }}-cluster-{{ $cluster_key }}
  namespace: {{ include  "argo-cd.namespace" $ | quote }}
  labels:
    {{- include "argo-cd.labels" (dict "context" $) | nindent 4 }}
    {{- with $cluster_value.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    argocd.argoproj.io/secret-type: cluster
  {{- with $cluster_value.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: Opaque
stringData:
  {{- if $cluster_value.shard }}
  shard: {{ $cluster_value.shard | quote }}
  {{- end }}
  name: {{ required "A valid .Values.configs.clusterCredentials.CLUSTERNAME.name entry is required!" $cluster_key }}
  server: {{ required "A valid .Values.configs.clusterCredentials.CLUSTERNAME.server entry is required!" $cluster_value.server }}
  {{- if $cluster_value.namespaces }}
  namespaces: {{ $cluster_value.namespaces }}
    {{- if $cluster_value.clusterResources }}
  clusterResources: {{ $cluster_value.clusterResources | quote }}
    {{- end }}
  {{- end }}
  {{- if $cluster_value.project }}
  project: {{ $cluster_value.project | quote }}
  {{- end }}
  config: |
    {{- required "A valid .Values.configs.clusterCredentials.CLUSTERNAME.config entry is required!" $cluster_value.config | toRawJson | nindent 4 }}
{{- end }}

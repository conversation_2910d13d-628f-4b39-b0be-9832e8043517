{{- if and (or .Values.externalRedis.username .Values.externalRedis.password) (not .Values.externalRedis.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: argocd-redis
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" $) | nindent 4 }}
  {{- with .Values.externalRedis.secretAnnotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: Opaque
data:
  {{- with .Values.externalRedis.username }}
  redis-username: {{ . | b64enc }}
  {{- end }}
  {{- with .Values.externalRedis.password }}
  redis-password: {{ . | b64enc }}
  {{- end }}
{{- end }}

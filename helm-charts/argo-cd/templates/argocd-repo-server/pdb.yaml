{{- if .Values.repoServer.pdb.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "argo-cd.repoServer.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
    {{- with .Values.repoServer.pdb.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.repoServer.pdb.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- with .Values.repoServer.pdb.maxUnavailable }}
  maxUnavailable: {{ . }}
  {{- else }}
  minAvailable: {{ .Values.repoServer.pdb.minAvailable | default 0 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.repoServer.name) | nindent 6 }}
{{- end }}

{{- if .Values.repoServer.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.repoServer.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "argo-cd.repoServer.serviceAccountName" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  {{- with .Values.repoServer.serviceAccount.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
    {{- with .Values.repoServer.serviceAccount.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}

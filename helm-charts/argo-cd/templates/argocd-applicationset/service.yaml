apiVersion: v1
kind: Service
metadata:
{{- if .Values.applicationSet.service.annotations }}
  annotations:
  {{- range $key, $value := .Values.applicationSet.service.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
  name: {{ template "argo-cd.applicationSet.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
{{- with .Values.applicationSet.service.labels }}
{{- toYaml . | nindent 4 }}
{{- end }}
spec:
  type: {{ .Values.applicationSet.service.type }}
  {{- include "argo-cd.dualStack" . | indent 2 }}
  ports:
  - name: {{ .Values.applicationSet.service.portName }}
    port: {{ .Values.applicationSet.service.port }}
    targetPort: webhook
  selector:
    {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.applicationSet.name) | nindent 4 }}

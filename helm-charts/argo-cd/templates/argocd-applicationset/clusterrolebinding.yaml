{{- if .Values.applicationSet.allowAnyNamespace }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "argo-cd.applicationSet.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "argo-cd.applicationSet.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "argo-cd.applicationSet.serviceAccountName" . }}
    namespace: {{ include "argo-cd.namespace" . }}
{{- end }}

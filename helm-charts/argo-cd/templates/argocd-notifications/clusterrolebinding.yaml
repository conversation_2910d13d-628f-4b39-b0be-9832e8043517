{{- if and .Values.notifications.enabled .Values.createClusterRoles }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "argo-cd.notifications.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "argo-cd.notifications.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "argo-cd.notifications.serviceAccountName" . }}
  namespace: {{ include "argo-cd.namespace" . }}
{{- end }}

{{- if .Values.notifications.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "argo-cd.notifications.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "argo-cd.notifications.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "argo-cd.notifications.serviceAccountName" . }}
    namespace: {{ include "argo-cd.namespace" . }}
{{- end }}

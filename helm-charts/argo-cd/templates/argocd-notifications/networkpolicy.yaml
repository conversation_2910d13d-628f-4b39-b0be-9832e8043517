{{- if and .Values.notifications.enabled .Values.global.networkPolicy.create .Values.notifications.metrics.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "argo-cd.notifications.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
spec:
  ingress:
  - from:
    - namespaceSelector: {}
    ports:
    - port: metrics
  podSelector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.notifications.name) | nindent 6 }}
  policyTypes:
  - Ingress
{{- end }}

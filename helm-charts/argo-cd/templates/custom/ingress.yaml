apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: argocd-server-http-ingress
  namespace: argocd
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    nginx.ingress.kubernetes.io/auth-tls-error-page: "https://pki-error.dvb.corpinter.net/client-error"
    nginx.ingress.kubernetes.io/auth-tls-verify-client: "on"
    nginx.ingress.kubernetes.io/auth-tls-secret: "argocd/sbod-argocd-dvb-corpinter-net-cacerts-tlsauth-0"
    nginx.ingress.kubernetes.io/auth-tls-verify-depth: "4"
    nginx.ingress.kubernetes.io/auth-tls-pass-certificate-to-upstream: "true"
spec:
  rules:
  - http:
      paths:
      - pathType: Prefix
        path: /
        backend:
          service:
            name: argocd-server
            port: 
              name: https
    host: sbod-argocd.dvb.corpinter.net
  tls:
  - hosts:
    - sbod-argocd.dvb.corpinter.net
    secretName: sbod-argocd-dvb-corpinter-net-0
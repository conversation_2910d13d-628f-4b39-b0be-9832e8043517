{{- if .Values.dex.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "argo-cd.dex.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.dex.name "name" .Values.dex.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "argo-cd.dex.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "argo-cd.dex.serviceAccountName" . }}
  namespace: {{ include "argo-cd.namespace" . }}
{{- end }}

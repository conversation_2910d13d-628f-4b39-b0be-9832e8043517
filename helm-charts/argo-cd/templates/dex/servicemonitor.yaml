{{- if and (.Capabilities.APIVersions.Has "monitoring.coreos.com/v1") .Values.dex.metrics.enabled .Values.dex.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "argo-cd.dex.fullname" . }}
  namespace: {{ default (include  "argo-cd.namespace" .) .Values.dex.metrics.serviceMonitor.namespace | quote }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.dex.name "name" .Values.dex.name) | nindent 4 }}
    {{- with .Values.dex.metrics.serviceMonitor.selector }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.dex.metrics.serviceMonitor.additionalLabels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.dex.metrics.serviceMonitor.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  endpoints:
    - port: {{ .Values.dex.metrics.service.portName }}
      {{- with .Values.dex.metrics.serviceMonitor.interval }}
      interval: {{ . }}
      {{- end }}
      path: /metrics
      {{- with .Values.dex.metrics.serviceMonitor.relabelings }}
      relabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.dex.metrics.serviceMonitor.metricRelabelings }}
      metricRelabelings:
        {{- toYaml . |nindent 8 }}
      {{- end }}
      honorLabels: {{ .Values.dex.metrics.serviceMonitor.honorLabels }}
      {{- with .Values.dex.metrics.serviceMonitor.scheme }}
      scheme: {{ . }}
      {{- end }}
      {{- with .Values.dex.metrics.serviceMonitor.tlsConfig }}
      tlsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  namespaceSelector:
    matchNames:
      - {{ include "argo-cd.namespace" . }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "component" .Values.dex.name "name" .Values.dex.name) | nindent 6 }}
{{- end }}

# Argo CD Chart

A Helm chart for Argo CD, a declarative, GitOps continuous delivery tool for Kubernetes.

Source code can be found here:

{{ template "chart.sourcesList" . }}

This is a **community maintained** chart. This chart installs [argo-cd](https://argo-cd.readthedocs.io/en/stable/), a declarative, GitOps continuous delivery tool for Kubernetes.

The default installation is intended to be similar to the provided Argo CD [releases](https://github.com/argoproj/argo-cd/releases).

If you want to avoid including sensitive information unencrypted (clear text) in your version control, make use of the [declarative setup] of Argo CD.
For instance, rather than adding repositories and their keys in your Helm values, you could deploy [SealedSecrets](https://github.com/bitnami-labs/sealed-secrets) with contents as seen in this [repositories section](https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#repositories) or any other secrets manager service (i.e. HashiCorp Vault, AWS/GCP Secrets Manager, etc.).

## High Availability

This chart installs the non-HA version of Argo CD by default. If you want to run Argo CD in HA mode, you can use one of the example values in the next sections.
Please also have a look into the upstream [Operator Manual regarding High Availability](https://argo-cd.readthedocs.io/en/stable/operator-manual/high_availability/) to understand how scaling of Argo CD works in detail.

> **Warning:**
> You need at least 3 worker nodes as the HA mode of redis enforces Pods to run on separate nodes.

### HA mode with autoscaling

```yaml
redis-ha:
  enabled: true

controller:
  replicas: 1

server:
  autoscaling:
    enabled: true
    minReplicas: 2

repoServer:
  autoscaling:
    enabled: true
    minReplicas: 2

applicationSet:
  replicas: 2
```

### HA mode without autoscaling

```yaml
redis-ha:
  enabled: true

controller:
  replicas: 1

server:
  replicas: 2

repoServer:
  replicas: 2

applicationSet:
  replicas: 2
```

## Ingress configuration

Please refer to the [Operator Manual](https://argo-cd.readthedocs.io/en/stable/operator-manual/ingress/#ingress-configurationh) for details as the samples
below corespond to their respective sections.

### SSL-Passthrough

The `tls: true` option will expect that the `argocd-server-tls` secret exists as Argo CD server loads TLS certificates from this place.

```yaml
global:
  domain: argocd.example.com

certificate:
  enabled: true

server:
  ingress:
    enabled: true
    ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    tls: true
```

### SSL Termination at Ingress Controller

```yaml
global:
  domain: argocd.example.com

configs:
  params:
    server.insecure: true

server:
  ingress:
    enabled: true
    ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    extraTls:
      - hosts:
        - argocd.example.com
        # Based on the ingress controller used secret might be optional
        secretName: wildcard-tls
```

> **Note:**
> If you don't plan on using a wildcard certificate it's also possible to use `tls: true` without `extraTls` section.

### Multiple ingress resources for gRPC protocol support

Use `ingressGrpc` section if your ingress controller supports only a single protocol per Ingress resource (i.e.: Contour).

```yaml
global:
  domain: argocd.example.com

configs:
  params:
    server.insecure: true

server:
  ingress:
    enabled: true
    ingressClassName: contour-internal
    extraTls:
      - hosts:
        - argocd.example.com
        secretName: wildcard-tls

   ingressGrpc:
     enabled: true
     ingressClassName: contour-internal
     extraTls:
      - hosts:
        - grpc.argocd.example.com
        secretName: wildcard-tls
```

### Multiple ingress domains

```yaml
global:
  domain: argocd.example.com

server:
  ingress:
    enabled: true
    ingressClassName: nginx
    annotations:
      cert-manager.io/cluster-issuer: "<my-issuer>"
      nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    tls: true
    extraHosts:
      - name: argocd-alias.example.com
        path: /
```

### AWS Application Load Balancer

Refer to the Operator Manual for [AWS Application Load Balancer mode](https://argo-cd.readthedocs.io/en/stable/operator-manual/ingress/#aws-application-load-balancers-albs-and-classic-elb-http-mode).
The provided example assumes you are using TLS off-loading via AWS ACM service.

> **Note:**
> Using `controller: aws` creates additional service for gRPC traffic and it's no longer need to use `ingressGrpc` configuration section.

```yaml
global:
  domain: argocd.example.com

configs:
  params:
    server.insecure: true

server:
  ingress:
    enabled: true
    controller: aws
    ingressClassName: alb
    annotations:
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/backend-protocol: HTTP
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":80}, {"HTTPS":443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
    aws:
      serviceType: ClusterIP # <- Used with target-type: ip
      backendProtocolVersion: GRPC
```

### GKE Application Load Balancer

The implementation will populate `ingressClassName`, `networking.gke.io/managed-certificates` and `networking.gke.io/v1beta1.FrontendConfig` annotations
automatically if you provide configuration for GKE resources.

```yaml
global:
  domain: argocd.example.com

configs:
  params:
    server.insecure: true

server:
  service:
    annotations:
      cloud.google.com/neg: '{"ingress": true}'
      cloud.google.com/backend-config: '{"ports": {"http":"argocd-server"}}'

  ingress:
    enabled: true
    controller: gke
    gke:
      backendConfig:
        healthCheck:
          checkIntervalSec: 30
          timeoutSec: 5
          healthyThreshold: 1
          unhealthyThreshold: 2
          type: HTTP
          requestPath: /healthz
          port: 8080
      frontendConfig:
        redirectToHttps:
          enabled: true  
      managedCertificate:
        enabled: true
```


## Synchronizing Changes from Original Repository

In the original [Argo CD repository](https://github.com/argoproj/argo-cd/) an [`manifests/install.yaml`](https://github.com/argoproj/argo-cd/blob/master/manifests/install.yaml) is generated using `kustomize`. It's the basis for the installation as [described in the docs](https://argo-cd.readthedocs.io/en/stable/getting_started/#1-install-argo-cd).

When installing Argo CD using this helm chart the user should have a similar experience and configuration rolled out. Hence, it makes sense to try to achieve a similar output of rendered `.yaml` resources when calling `helm template` using the default settings in `values.yaml`.

To update the templates and default settings in `values.yaml` it may come in handy to look up the diff of the `manifests/install.yaml` between two versions accordingly. This can either be done directly via github and look for `manifests/install.yaml`:

https://github.com/argoproj/argo-cd/compare/v1.8.7...v2.0.0#files_bucket

Or you clone the repository and do a local `git-diff`:

```bash
git clone https://github.com/argoproj/argo-cd.git
cd argo-cd
git diff v1.8.7 v2.0.0 -- manifests/install.yaml
```

Changes in the `CustomResourceDefinition` resources shall be fixed easily by copying 1:1 from the [`manifests/crds` folder](https://github.com/argoproj/argo-cd/tree/master/manifests/crds) into this [`charts/argo-cd/templates/crds` folder](https://github.com/argoproj/argo-helm/tree/master/charts/argo-cd/templates/crds).

### Custom resource definitions

Some users would prefer to install the CRDs _outside_ of the chart. You can disable the CRD installation of this chart by using `--set crds.install=false` when installing the chart.

Helm cannot upgrade custom resource definitions in the `<chart>/crds` folder [by design](https://helm.sh/docs/chart_best_practices/custom_resource_definitions/#some-caveats-and-explanations). Starting with 5.2.0, the CRDs have been moved to `<chart>/templates` to address this design decision.

If you are using Argo CD chart version prior to 5.2.0 or have elected to manage the Argo CD CRDs outside of the chart, please use `kubectl` to upgrade CRDs manually from [templates/crds](templates/crds/) folder or via the manifests from the upstream project repo:

```bash
kubectl apply -k "https://github.com/argoproj/argo-cd/manifests/crds?ref=<appVersion>"

# Eg. version v2.4.9
kubectl apply -k "https://github.com/argoproj/argo-cd/manifests/crds?ref=v2.4.9"
```

## Changelog

For full list of changes please check ArtifactHub [changelog].

Highlighted versions provide information about additional steps that should be performed by user when upgrading to newer version.

### 7.0.0

We changed the type of `.Values.configs.clusterCredentials` from `list` to `object`.
If you used the value, please migrate like below.

```yaml
# before
configs:
  clusterCredentials:
    - mycluster:
      server: https://mycluster.example.com
      labels: {}
      annotations: {}
      # ...

# after
configs:
  clusterCredentials:
    mycluster:
      server: https://mycluster.example.com
      labels: {}
      annotations: {}
      # ...
```

### 6.10.0

This version introduces authentication for Redis to mitigate GHSA-9766-5277-j5hr.

#### How to rotate Redis secret?

Upstream steps in the [FAQ] are not enough, since we chose a different approach.
(We use a Kubernetes Job with [Chart Hooks] to create the auth secret `argocd-redis`.)

Steps to roteate the secret when using the helm chart (bold step is additional to upstream):
* Delete `argocd-redis` secret in the namespace where Argo CD is installed. 
  ```bash
  kubectl delete secret argocd-redis -n <argocd namespace>
  ```
* **Perform a helm upgrade**
  ```bash
  helm upgrade argocd argo/argo-cd --reuse-values --wait
  ```
* If you are running Redis in HA mode, restart Redis in HA.
  ```bash
  kubectl rollout restart deployment argocd-redis-ha-haproxy
  kubectl rollout restart statefulset argocd-redis-ha-server
  ```
* If you are running Redis in non-HA mode, restart Redis.
  ```bash
  kubectl rollout restart deployment argocd-redis
  ```
* Restart other components.
  ```bash
  kubectl rollout restart deployment argocd-server argocd-repo-server
  kubectl rollout restart statefulset argocd-application-controller
  ```

### 6.9.0
ApplicationSet controller is always created to follow [upstream's manifest](https://github.com/argoproj/argo-cd/blob/v2.11.0/manifests/core-install/kustomization.yaml#L9).  

### 6.4.0

Added support for application controller dynamic cluster distribution.
Please refer to [the docs](https://argo-cd.readthedocs.io/en/stable/operator-manual/dynamic-cluster-distribution) for more information.

Added env variables to handle the non-standard names generated by the helm chart.
Here are the [docs](https://argo-cd.readthedocs.io/en/release-2.9/user-guide/environment-variables/)
and [code](https://github.com/argoproj/argo-cd/blob/99723143b96ceec9ef5b0a7feb7b4f4b0dce3497/common/common.go#L252)

### 6.1.0

Added support for global domain used by all components.

### 6.0.0

This version **removes support for**:

* deprecated component options `logLevel` and `logFormat`
* deprecated component arguments `<components>.args.<feature>` that were replaced with `configs.params`
* deprecated configuration `server.config` that was replaced with `configs.cm`
* deprecated configuration `server.rbacConfig` that was replaced with `configs.rbac`

Major version also contains breaking **changes related to Argo CD Ingress** resources that were hard to extend and maintain for various ingress controller implementations.
Please review your setup and adjust to new configuration options:

* catch all rule was removed for security reasons. If you need this please use `server.ingress.extraRules` to provide ingress rule without hostname
* ingress rule for `paths` changed to `path` as there is only single Argo CD backend path
* ingress rule for `hosts` changed to `hostname` as there can be only single SSO redirect for given hostname
* ingress TLS for server uses by default `argocd-server-tls` secret required by Argo CD server, additional ingresses are using `<hostname>-tls` secret when `tls: true`
* additional hostnames and routing can be provided via `extraHosts` configuration section
* additional TLS secrets can be provided via `extraTls` configuration section

Please refer to [ingress configuration](#ingress-configuration) for examples.

### 5.53.0

Argocd-repo-server can now optionally use Persistent Volumes for its mountpoints instead of only emptydir()

### 5.52.0

Because [Argo CD Extensions] is now deprecated and no further changes will be made, we switched to [Argo CD Extension Installer], adding an Argo CD Extension Installer to init-container in the Argo CD API server.
If you used old mechanism, please move to new mechanism. For more details, please refer `.Values.server.extensions` in values.yaml.

### 5.35.0

This version supports Kubernetes version `>=1.23.0-0`. The current supported version of Kubernetes is v1.24 or later and we align with the Amazon EKS calendar, because many AWS users follow a conservative approach.

Please see more information about EoL: [Amazon EKS EoL][EKS EoL].


### 5.31.0
The manifests are now using [`tini` as entrypoint][tini], instead of `entrypoint.sh`. Until Argo CD v2.8, `entrypoint.sh` is retained for upgrade compatibility.
This means that the deployment manifests have to be updated after upgrading to Argo CD v2.7, and before upgrading to Argo CD v2.8 later.
In case the manifests are updated before moving to Argo CD v2.8, the containers will not be able to start.

### 5.26.0

This version adds support for Config Management Plugins using the sidecar model and configured in a ConfigMap named `argocd-cmp-cm`. 
Users will need to migrate from the previous `argocd-cm` ConfigMap method to using the sidecar method before Argo CD v2.8. See the [Argo CD CMP migration guide](https://argo-cd.readthedocs.io/en/stable/operator-manual/config-management-plugins/#migrating-from-argocd-cm-plugins) for more specifics.

To migrate your plugins, you can now set the `configs.cmp.create` to `true` and move your plugins from `configs.cm` to `configs.cmp.plugins`.
You will also need to configure the sidecar containers under `repoServer.extraContainers` and ensure you are mounting any custom volumes you need from `repoServer.volumes` into here also.

### 5.24.0

This version adds additional global parameters for scheduling (`nodeSelector`, `tolerations`, `topologySpreadConstraints`).
Default `global.affinity` rules can be disabled when `none` value is used for the preset.

### 5.22.0

This version adds `global.affinity` options that are used as a presets. Override on component level works as before and replaces the default preset completely.

### 5.19.0

This version consolidates config for custom repository TLS certificates and SSH known hosts. If you provided these values (`configs.knownHosts.*`, `configs.knownHostsAnnotations`, `configs.tlsCerts`, `configs.tlsCertsAnnotations`) please move them into new `configs.ssh` and `configs.tls` sections.
You can also use new option `configs.ssh.extraHosts` to configure your SSH keys without maintaing / overwritting keys for public Git repositories.

### 5.13.0

This version reduces history limit for Argo CD deployment replicas to 3 to provide more visibility for Argo CD deployments that manage itself. If you need more deployment revisions for rollbacks set `global.revisionHistoryLimit` parameter.

### 5.12.0

If Argo CD is managing termination of TLS and you are using `configs.secret.argocdServerTlsConfig` option to provide custom TLS configuration for this chart, please use `server.certificate` or `server.certificateSecret` instead.
For the secrets for tls termination, please use a secret named `argocd-server-tls` instead of `argocd-secret`.
For the technical details please check the [Argo CD documentation](https://argo-cd.readthedocs.io/en/stable/operator-manual/tls/#tls-certificates-used-by-argocd-server). When transitioning from the one secret to the other pay attention to `tls.key` and `tls.crt` keys.

### 5.10.0

This version hardens security by configuring default container security contexts and adds hard requirement for Kubernetes 1.22+ to work properly.
The change aligns chart with officially [supported versions](https://argo-cd.readthedocs.io/en/release-2.5/operator-manual/installation/#supported-versions) by upstream project.

### 5.7.0

This version introcudes new `configs.cm` and `configs.rbac` sections that replaces `server.config` and `server.rbacConfig` respectively.
Please move your current configuration to the new place. The Argo CD RBAC config now also sets defaults in the `argocd-rbac-cm`.
If you have manually created this ConfigMap please ensure templating is disabled so you will not lose your changes.

### 5.5.20

This version moved API version templates into dedicated helper. If you are using these in your umbrella
chart please migrate your templates to pattern `argo-cd.apiVersion.<component>`.

### 5.5.0

This version introduces new `configs.params` section that replaces command line arguments for containers.
Please refer to documentation in values.yaml for migrating the configuration.

### 5.2.0

Custom resource definitions were moved to `templates` folder so they can be managed by Helm.

To adopt already created CRDs, please use following command:

```bash
YOUR_ARGOCD_NAMESPACE="" # e.g. argo-cd
YOUR_ARGOCD_RELEASENAME="" # e.g. argo-cd

for crd in "applications.argoproj.io" "applicationsets.argoproj.io" "argocdextensions.argoproj.io" "appprojects.argoproj.io"; do
  kubectl label --overwrite crd $crd app.kubernetes.io/managed-by=Helm
  kubectl annotate --overwrite crd $crd meta.helm.sh/release-namespace="$YOUR_ARGOCD_NAMESPACE"
  kubectl annotate --overwrite crd $crd meta.helm.sh/release-name="$YOUR_ARGOCD_RELEASENAME"
done
```

### 5.0.0

This version **removes support for**:

- deprecated repository credentials (parameter `configs.repositoryCredentials`)
- option to run application controller as a Deployment
- the parameters `server.additionalApplications` and `server.additionalProjects`

Please carefully read the following section if you are using these parameters!

In order to upgrade Applications and Projects safely against CRDs' upgrade, `server.additionalApplications` and `server.additionalProjects` are moved to [argocd-apps](../argocd-apps).

If you are using `server.additionalApplications` or `server.additionalProjects`, you can adopt to [argocd-apps](../argocd-apps) as below:

1. Add [helm.sh/resource-policy annotation](https://helm.sh/docs/howto/charts_tips_and_tricks/#tell-helm-not-to-uninstall-a-resource) to avoid resources being removed by upgrading Helm chart

You can keep your existing CRDs by adding `"helm.sh/resource-policy": keep` on `additionalAnnotations`, under `server.additionalApplications` and `server.additionalProjects` blocks, and running `helm upgrade`.

e.g:

```yaml
server:
  additionalApplications:
    - name: guestbook
      namespace: argocd
      additionalLabels: {}
      additionalAnnotations:
        "helm.sh/resource-policy": keep # <-- add this
      finalizers:
      - resources-finalizer.argocd.argoproj.io
      project: guestbook
      source:
        repoURL: https://github.com/argoproj/argocd-example-apps.git
        targetRevision: HEAD
        path: guestbook
        directory:
          recurse: true
      destination:
        server: https://kubernetes.default.svc
        namespace: guestbook
      syncPolicy:
        automated:
          prune: false
          selfHeal: false
      ignoreDifferences:
      - group: apps
        kind: Deployment
        jsonPointers:
        - /spec/replicas
      info:
      - name: url
        value: https://argoproj.github.io/
```

You can also keep your existing CRDs by running the following scripts.

```bash
# keep Applications
for app in "guestbook"; do
  kubectl annotate --overwrite application $app helm.sh/resource-policy=keep
done

# keep Projects
for project in "guestbook"; do
  kubectl annotate --overwrite appproject $project helm.sh/resource-policy=keep
done
```

2. Upgrade argo-cd Helm chart to v5.0.0

3. Remove keep [helm.sh/resource-policy annotation](https://helm.sh/docs/howto/charts_tips_and_tricks/#tell-helm-not-to-uninstall-a-resource)

```bash
# delete annotations from Applications
for app in "guestbook"; do
  kubectl annotate --overwrite application $app helm.sh/resource-policy-
done

# delete annotations from Projects
for project in "guestbook"; do
  kubectl annotate --overwrite appproject $project helm.sh/resource-policy-
done
```

4. Adopt existing resources to [argocd-apps](../argocd-apps)

### 4.9.0

This version starts to use upstream image with applicationset binary. Start command was changed from `applicationset-controller` to `argocd-applicationset-controller`

### 4.3.*

With this minor version, the notification notifier's `service.slack` is no longer configured by default.

### 4.0.0 and above

This helm chart version deploys Argo CD v2.3. The Argo CD Notifications and ApplicationSet are part of Argo CD now. You no longer need to install them separately. The Notifications and ApplicationSet components **are bundled into default** Argo CD installation.
Please read the [v2.2 to 2.3 upgrade instructions] in the upstream repository.

### 3.13.0

This release removes the flag `--staticassets` from argocd server as it has been dropped upstream. If this flag needs to be enabled e.g for older releases of Argo CD, it can be passed via the `server.extraArgs` field

### 3.10.2

Argo CD has recently deprecated the flag `--staticassets` and from chart version `3.10.2` has been disabled by default
It can be re-enabled by setting `server.staticAssets.enabled` to true

### 3.8.1

This bugfix version potentially introduces a rename (and recreation) of one or more ServiceAccounts. It _only happens_ when you use one of these customization:

```yaml
# Case 1) - only happens when you do not specify a custom name (repoServer.serviceAccount.name)
repoServer:
  serviceAccount:
    create: true

# Case 2)
controller:
  serviceAccount:
    name: "" # or <nil>

# Case 3)
dex:
  serviceAccount:
    name: "" # or <nil>

# Case 4)
server:
  serviceAccount:
    name: "" # or <nil>
```

Please check if you are affected by one of these cases **before you upgrade**, especially when you use **cloud IAM roles for service accounts.** (eg. IRSA on AWS or Workload Identity for GKE)

### 3.2.*

With this minor version we introduced the evaluation for the ingress manifest (depending on the capabilities version), See [Pull Request](https://github.com/argoproj/argo-helm/pull/637).
[Issue 703](https://github.com/argoproj/argo-helm/issues/703) reported that the capabilities evaluation is **not handled correctly when deploying the chart via an Argo CD instance**,
especially deploying on clusters running a cluster version prior to `1.19` (which misses  `Ingress` on apiVersion `networking.k8s.io/v1`).

If you are running a cluster version prior to `1.19` you can avoid this issue by directly installing chart version `3.6.0` and setting `kubeVersionOverride` like:

```yaml
kubeVersionOverride: "1.18.0"
```

Then you should no longer encounter this issue.


### 3.0.0 and above

Helm apiVersion switched to `v2`. Requires Helm `3.0.0` or above to install. [Read More](https://helm.sh/blog/migrate-from-helm-v2-to-helm-v3/) on how to migrate your release from Helm 2 to Helm 3.

### 2.14.7 and above

The `matchLabels` key in the Argo CD Application Controller is no longer hard-coded. Note that labels are immutable so caution should be exercised when making changes to this resource.

### 2.10.x to 2.11.0

The application controller is now available as a `StatefulSet` when the `controller.enableStatefulSet` flag is set to true. Depending on your Helm deployment this may be a downtime or breaking change if enabled when using HA and will become the default in 3.x.

### 1.8.7 to 2.x.x

`controller.extraArgs`, `repoServer.extraArgs` and `server.extraArgs`  are now arrays of strings instead of a map

What was

```yaml
server:
  extraArgs:
    insecure: ""
```

is now

```yaml
server:
  extraArgs:
  - --insecure
```

## Prerequisites

- {{ template "chart.kubeVersionLine" . }}
  - We align with [Amazon EKS calendar][EKS EoL] because there are many AWS users and it's a conservative approach.
  - Please check [Support Matrix of Argo CD][Kubernetes Compatibility Matrix] for official info.
- Helm v3.0.0+

## Installing the Chart

To install the chart with the release name `my-release`:

```console
$ helm repo add argo https://argoproj.github.io/argo-helm
"argo" has been added to your repositories

$ helm install my-release argo/argo-cd
NAME: my-release
...
```

## General parameters

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if not (or  (hasPrefix "global" .Key) (hasPrefix "configs" .Key) (hasPrefix "controller" .Key) (hasPrefix "repoServer" .Key) (hasPrefix "server" .Key) (hasPrefix "applicationSet" .Key) (hasPrefix "notifications" .Key) (hasPrefix "dex" .Key) (hasPrefix "redis" .Key) (hasPrefix "externalRedis" .Key) ) }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## Global Configs

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "global" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## Argo CD Configs

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "configs" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## Argo CD Controller

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "controller" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## Argo Repo Server

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "repoServer" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## Argo Server

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if and (hasPrefix "server" .Key) (not (hasPrefix "server.additional" .Key)) }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## Dex

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "dex" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## Redis

### Option 1 - Single Redis instance (default option)

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "redis." .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

### Option 2 - Redis HA

This option uses the following third-party chart to bootstrap a clustered Redis: https://github.com/DandyDeveloper/charts/tree/master/charts/redis-ha.
For all available configuration options, please read upstream README and/or chart source.
The main options are listed here:

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "redis-ha" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}
| redis-ha.exporter.image | string | `nil` (follows subchart default) | Exporter image |
| redis-ha.exporter.tag | string | `nil` (follows subchart default) | Exporter tag |
| redis-ha.haproxy.image.repository | string | `nil` (follows subchart default) | HAProxy Image Repository |
| redis-ha.haproxy.image.tag | string | `nil` (follows subchart default) | HAProxy Image Tag |
| redis-ha.image.repository | string | `nil` (follows subchart default) | Redis image repository |

### Option 3 - External Redis

If you want to use an existing Redis (eg. a managed service from a cloud provider), you can use these parameters:

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "externalRedis" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

### Redis secret-init

The helm chart deploys a Job to setup a random password which is used to secure the Redis. The Redis password is stored in Kubernetes secret `argocd-redis` with key `auth` in the namespace where Argo CD is installed.
If you use an External Redis (See Option 3 above), this Job is not deployed.

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "redisSecretInit" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## ApplicationSet

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "applicationSet" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

## Notifications

| Key | Type | Default | Description |
|-----|------|---------|-------------|
{{- range .Values }}
  {{- if hasPrefix "notifications" .Key }}
| {{ .Key }} | {{ .Type }} | {{ if .Default }}{{ .Default }}{{ else }}{{ .AutoDefault }}{{ end }} | {{ if .Description }}{{ .Description }}{{ else }}{{ .AutoDescription }}{{ end }} |
  {{- end }}
{{- end }}

----------------------------------------------
Autogenerated from chart metadata using [helm-docs](https://github.com/norwoodj/helm-docs)

[Argo CD RBAC policy]: https://argo-cd.readthedocs.io/en/stable/operator-manual/rbac/
[affinity]: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
[BackendConfigSpec]: https://cloud.google.com/kubernetes-engine/docs/concepts/backendconfig#backendconfigspec_v1beta1_cloudgooglecom
[CSS styles]: https://argo-cd.readthedocs.io/en/stable/operator-manual/custom-styles/
[changelog]: https://artifacthub.io/packages/helm/argo/argo-cd?modal=changelog
[Chart Hooks]: https://helm.sh/docs/topics/charts_hooks/
[DNS configuration]: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/
[external cluster credentials]: https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup/#clusters
[FAQ]: https://argo-cd.readthedocs.io/en/stable/faq/
[FrontendConfigSpec]: https://cloud.google.com/kubernetes-engine/docs/how-to/ingress-features#configuring_ingress_features_through_frontendconfig_parameters
[declarative setup]: https://argo-cd.readthedocs.io/en/stable/operator-manual/declarative-setup
[gRPC-ingress]: https://argo-cd.readthedocs.io/en/stable/operator-manual/ingress/
[GnuPG]: https://argo-cd.readthedocs.io/en/stable/user-guide/gpg-verification/
[HPA]: https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/
[MetricRelabelConfigs]: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#metric_relabel_configs
[Node selector]: https://kubernetes.io/docs/user-guide/node-selection/
[PodDisruptionBudget]: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/#pod-disruption-budgets
[probe]: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#container-probes
[RelabelConfigs]: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config
[Tolerations]: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
[TopologySpreadConstraints]: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/
[values.yaml]: values.yaml
[v2.2 to 2.3 upgrade instructions]: https://github.com/argoproj/argo-cd/blob/v2.3.0/docs/operator-manual/upgrading/2.2-2.3.md
[tini]: https://github.com/argoproj/argo-cd/pull/12707
[EKS EoL]: https://endoflife.date/amazon-eks
[Kubernetes Compatibility Matrix]: https://argo-cd.readthedocs.io/en/stable/operator-manual/installation/#supported-versions
[Applications in any namespace]: https://argo-cd.readthedocs.io/en/stable/operator-manual/app-any-namespace/#applications-in-any-namespace
[Argo CD Extensions]: https://github.com/argoproj-labs/argocd-extensions?tab=readme-ov-file#deprecation-notice
[Argo CD Extension Installer]: https://github.com/argoproj-labs/argocd-extension-installer

apiVersion: v2
appVersion: v2.13.1
kubeVersion: ">=1.25.0-0"
description: A Helm chart for Argo CD, a declarative, GitOps continuous delivery tool for Kubernetes.
name: argo-cd
version: 7.7.6
home: https://github.com/argoproj/argo-helm
icon: https://argo-cd.readthedocs.io/en/stable/assets/logo.png
sources:
  - https://github.com/argoproj/argo-helm/tree/main/charts/argo-cd
  - https://github.com/argoproj/argo-cd
keywords:
  - argoproj
  - argocd
  - gitops
maintainers:
  - name: argoproj
    url: https://argoproj.github.io/
#dependencies:
#  - name: redis-ha
#    version: 4.29.4
#    repository: https://dandydeveloper.github.io/charts/
#    condition: redis-ha.enabled
annotations:
  artifacthub.io/signKey: |
    fingerprint: 2B8F22F57260EFA67BE1C5824B11F800CD9D2252
    url: https://argoproj.github.io/argo-helm/pgp_keys.asc
  artifacthub.io/changes: |
    - kind: fixed
      description: REDIS_PASSWORD optional flag change

apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  name: sbod-ext-post-policy
  namespace: {{ .Values.meshedService.namespace }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: HTTPRoute
    name: sbod-ext-post-route
  requiredAuthenticationRefs:
    - name: sbod-core-post-authn
      kind: MeshTLSAuthentication
      group: policy.linkerd.io
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  name: sbod-ext-tgw-policy
  namespace: {{ .Values.meshedService.namespace }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: HTTPRoute
    name: sbod-ext-tgw-route
  requiredAuthenticationRefs:
    - name: tgw-sbod-ext-get-authn
      kind: MeshTLSAuthentication
      group: policy.linkerd.io
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  name: sbod-ext-probe-policy
  namespace: {{ .Values.meshedService.namespace }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: HTTPRoute
    name: sbod-ext-probe-route
  requiredAuthenticationRefs:
    - name: sbod-ext-probe-authn
      kind: NetworkAuthentication
      group: policy.linkerd.io

apiVersion: policy.linkerd.io/v1beta1
kind: HTTPRoute
metadata:
  name: sbod-ext-post-route
  namespace: {{ .Values.meshedService.namespace }}
spec:
  parentRefs:
    - name: sbod-ext-http
      kind: Server
      group: policy.linkerd.io
  rules:
    - matches:
        - path:
            value: "/ext/"
            type: "PathPrefix"
          method: POST
---
apiVersion: policy.linkerd.io/v1beta1
kind: HTTPRoute
metadata:
  name: sbod-ext-tgw-route
  namespace: {{ .Values.meshedService.namespace }}
spec:
  parentRefs:
    - name: sbod-ext-http
      kind: Server
      group: policy.linkerd.io
  rules:
    - matches:
        - path:
            value: "/ext/"
            type: "PathPrefix"
          method: GET
---
apiVersion: policy.linkerd.io/v1beta1
kind: HTTPRoute
metadata:
  name: sbod-ext-probe-route
  namespace: {{ .Values.meshedService.namespace }}
spec:
  parentRefs:
    - name: sbod-ext-http
      kind: Server
      group: policy.linkerd.io
  rules:
    - matches:
        - path:
            value: "/ext/actuator/health"
          method: GET

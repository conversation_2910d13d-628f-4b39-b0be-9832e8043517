---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  {{- with .Values.commonLabels }}
  labels: {{ toYaml . | trim | nindent 4 }}
  {{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
  name: ext-namespace-metadata-linkerd-config
  namespace: {{ .Release.Namespace }}
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]
  resourceNames: ["linkerd-config"]

---
###
### Identity Controller Service RBAC
###
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-identity
  labels:
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: ["authentication.k8s.io"]
  resources: ["tokenreviews"]
  verbs: ["create"]
# TODO(ver) Restrict this to the Linkerd namespace. See
# https://github.com/linkerd/linkerd2/issues/9367
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-identity
  labels:
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-identity
subjects:
- kind: ServiceAccount
  name: linkerd-identity
  namespace: {{.Release.Namespace}}
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: linkerd-identity
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}

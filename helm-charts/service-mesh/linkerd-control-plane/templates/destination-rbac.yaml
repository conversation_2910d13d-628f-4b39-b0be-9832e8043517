---
###
### Destination Controller Service
###
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-destination
  labels:
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: ["apps"]
  resources: ["replicasets"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["list", "get", "watch"]
- apiGroups: [""]
  resources: ["pods", "endpoints", "services", "nodes"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["linkerd.io"]
  resources: ["serviceprofiles"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["workload.linkerd.io"]
  resources: ["externalworkloads"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["create", "get", "update", "patch"]
  {{- if .Values.enableEndpointSlices }}
- apiGroups: ["discovery.k8s.io"]
  resources: ["endpointslices"]
  verbs: ["list", "get", "watch", "create", "update", "patch", "delete"]
  {{- end }}
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-destination
  labels:
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-destination
subjects:
- kind: ServiceAccount
  name: linkerd-destination
  namespace: {{.Release.Namespace}}
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: linkerd-destination
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}
---
{{- $host := printf "linkerd-sp-validator.%s.svc" .Release.Namespace }}
{{- $ca := genSelfSignedCert $host (list) (list $host) 365 }}
{{- if (not .Values.profileValidator.externalSecret) }}
kind: Secret
apiVersion: v1
metadata:
  name: linkerd-sp-validator-k8s-tls
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
type: kubernetes.io/tls
data:
  tls.crt: {{ ternary (b64enc (trim $ca.Cert)) (b64enc (trim .Values.profileValidator.crtPEM)) (empty .Values.profileValidator.crtPEM) }}
  tls.key: {{ ternary (b64enc (trim $ca.Key)) (b64enc (trim .Values.profileValidator.keyPEM)) (empty .Values.profileValidator.keyPEM) }}
---
{{- end }}
{{- include "linkerd.webhook.validation" .Values.profileValidator }}
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: linkerd-sp-validator-webhook-config
  {{- if or (.Values.profileValidator.injectCaFrom) (.Values.profileValidator.injectCaFromSecret) }}
  annotations:
  {{- if .Values.profileValidator.injectCaFrom }}
    cert-manager.io/inject-ca-from: {{ .Values.profileValidator.injectCaFrom }}
  {{- end }}
  {{- if .Values.profileValidator.injectCaFromSecret }}
    cert-manager.io/inject-ca-from-secret: {{ .Values.profileValidator.injectCaFromSecret }}
  {{- end }}
  {{- end }}
  labels:
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
webhooks:
- name: linkerd-sp-validator.linkerd.io
  namespaceSelector:
    {{- toYaml .Values.profileValidator.namespaceSelector | trim | nindent 4 }}
  clientConfig:
    service:
      name: linkerd-sp-validator
      namespace: {{ .Release.Namespace }}
      path: "/"
    {{- if and (empty .Values.profileValidator.injectCaFrom) (empty .Values.profileValidator.injectCaFromSecret) }}
    caBundle: {{ ternary (b64enc (trim $ca.Cert)) (b64enc (trim .Values.profileValidator.caBundle)) (empty .Values.profileValidator.caBundle) }}
    {{- end }}
  failurePolicy: {{.Values.webhookFailurePolicy}}
  admissionReviewVersions: ["v1", "v1beta1"]
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["linkerd.io"]
    apiVersions: ["v1alpha1", "v1alpha2"]
    resources: ["serviceprofiles"]
  sideEffects: None
---
{{- $host := printf "linkerd-policy-validator.%s.svc" .Release.Namespace }}
{{- $ca := genSelfSignedCert $host (list) (list $host) 365 }}
{{- if (not .Values.policyValidator.externalSecret) }}
kind: Secret
apiVersion: v1
metadata:
  name: linkerd-policy-validator-k8s-tls
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
type: kubernetes.io/tls
data:
  tls.crt: {{ ternary (b64enc (trim $ca.Cert)) (b64enc (trim .Values.policyValidator.crtPEM)) (empty .Values.policyValidator.crtPEM) }}
  tls.key: {{ ternary (b64enc (trim $ca.Key)) (b64enc (trim .Values.policyValidator.keyPEM)) (empty .Values.policyValidator.keyPEM) }}
---
{{- end }}
{{- include "linkerd.webhook.validation" .Values.policyValidator }}
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: linkerd-policy-validator-webhook-config
  {{- if or (.Values.policyValidator.injectCaFrom) (.Values.policyValidator.injectCaFromSecret) }}
  annotations:
  {{- if .Values.policyValidator.injectCaFrom }}
    cert-manager.io/inject-ca-from: {{ .Values.policyValidator.injectCaFrom }}
  {{- end }}
  {{- if .Values.policyValidator.injectCaFromSecret }}
    cert-manager.io/inject-ca-from-secret: {{ .Values.policyValidator.injectCaFromSecret }}
  {{- end }}
  {{- end }}
  labels:
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
webhooks:
- name: linkerd-policy-validator.linkerd.io
  namespaceSelector:
    {{- toYaml .Values.policyValidator.namespaceSelector | trim | nindent 4 }}
  clientConfig:
    service:
      name: linkerd-policy-validator
      namespace: {{ .Release.Namespace }}
      path: "/"
    {{- if and (empty .Values.policyValidator.injectCaFrom) (empty .Values.policyValidator.injectCaFromSecret) }}
    caBundle: {{ ternary (b64enc (trim $ca.Cert)) (b64enc (trim .Values.policyValidator.caBundle)) (empty .Values.policyValidator.caBundle) }}
    {{- end }}
  failurePolicy: {{.Values.webhookFailurePolicy}}
  admissionReviewVersions: ["v1", "v1beta1"]
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["policy.linkerd.io"]
    apiVersions: ["*"]
    resources:
    - authorizationpolicies
    - httproutes
    - networkauthentications
    - meshtlsauthentications
    - serverauthorizations
    - servers
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["gateway.networking.k8s.io"]
    apiVersions: ["*"]
    resources:
    - httproutes
  sideEffects: None
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: linkerd-policy
  labels:
    app.kubernetes.io/part-of: Linkerd
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apps
    resources:
      - deployments
    verbs:
      - get
  - apiGroups:
      - policy.linkerd.io
    resources:
      - authorizationpolicies
      - httproutes
      - meshtlsauthentications
      - networkauthentications
      - servers
      - serverauthorizations
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - httproutes
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - policy.linkerd.io
    resources:
      - httproutes/status
    verbs:
      - patch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - httproutes/status
    verbs:
      - patch
  - apiGroups:
      - workload.linkerd.io
    resources:
      - externalworkloads
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - create
      - get
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: linkerd-destination-policy
  labels:
    app.kubernetes.io/part-of: Linkerd
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: linkerd-policy
subjects:
  - kind: ServiceAccount
    name: linkerd-destination
    namespace: {{.Release.Namespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: remote-discovery
  namespace: {{.Release.Namespace}}
  labels:
    app.kubernetes.io/part-of: Linkerd
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - get
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: linkerd-destination-remote-discovery
  namespace: {{.Release.Namespace}}
  labels:
    app.kubernetes.io/part-of: Linkerd
    linkerd.io/control-plane-component: destination
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: remote-discovery
subjects:
  - kind: ServiceAccount
    name: linkerd-destination
    namespace: {{.Release.Namespace}}

---
###
### Proxy Injector
###
{{- $tree := deepCopy . }}
{{ $_ := set $tree.Values.proxy "workloadKind" "deployment" -}}
{{ $_ := set $tree.Values.proxy "component" "linkerd-proxy-injector" -}}
{{ $_ := set $tree.Values.proxy "waitBeforeExitSeconds" 0 -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    {{ include "partials.annotations.created-by" . }}
  labels:
    app.kubernetes.io/name: proxy-injector
    app.kubernetes.io/part-of: Linkerd
    app.kubernetes.io/version: {{.Values.linkerdVersion}}
    linkerd.io/control-plane-component: proxy-injector
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  name: linkerd-proxy-injector
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{.Values.controllerReplicas}}
  selector:
    matchLabels:
      linkerd.io/control-plane-component: proxy-injector
  {{- if .Values.deploymentStrategy }}
  strategy:
    {{- with .Values.deploymentStrategy }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  {{- end }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/proxy-injector-rbac.yaml") . | sha256sum }}
        {{ include "partials.annotations.created-by" . }}
        {{- include "partials.proxy.annotations" . | nindent 8}}
        {{- with .Values.podAnnotations }}{{ toYaml . | trim | nindent 8 }}{{- end }}
        config.linkerd.io/opaque-ports: "8443"
        config.linkerd.io/default-inbound-policy: "all-unauthenticated"
      labels:
        linkerd.io/control-plane-component: proxy-injector
        linkerd.io/control-plane-ns: {{.Release.Namespace}}
        linkerd.io/workload-ns: {{.Release.Namespace}}
        {{- include "partials.proxy.labels" $tree.Values.proxy | nindent 8}}
        {{- with .Values.podLabels }}{{ toYaml . | trim | nindent 8 }}{{- end }}
    spec:
      {{- with .Values.runtimeClassName }}
      runtimeClassName: {{ . | quote }}
      {{- end }}
      {{- if .Values.tolerations -}}
      {{- include "linkerd.tolerations" . | nindent 6 }}
      {{- end -}}
      {{- include "linkerd.node-selector" . | nindent 6 }}
      {{- $_ := set $tree "component" "proxy-injector" -}}
      {{- include "linkerd.affinity" $tree | nindent 6 }}
      containers:
      {{- if not (empty .Values.proxyInjectorProxyResources) }}
      {{- $r := merge .Values.proxyInjectorProxyResources .Values.proxy.resources }}
      {{- $_ := set $tree.Values.proxy "resources" $r }}
      {{- end }}
      {{- $_ := set $tree.Values.proxy "await" $tree.Values.proxy.await }}
      {{- $_ := set $tree.Values.proxy "loadTrustBundleFromConfigMap" true }}
      {{- $_ := set $tree.Values.proxy "podInboundPorts" "8443,9995" }}
      {{- /*
        The pod needs to accept webhook traffic, and we can't rely on that originating in the
        cluster network.
      */}}
      {{- $_ := set $tree.Values.proxy "defaultInboundPolicy" "all-unauthenticated" }}
      {{- $_ := set $tree.Values.proxy "capabilities" (dict "drop" (list "ALL")) }}
      {{- $_ := set $tree.Values.proxy "outboundDiscoveryCacheUnusedTimeout" "5s" }}
      {{- $_ := set $tree.Values.proxy "inboundDiscoveryCacheUnusedTimeout" "90s" }}
      {{- if not $tree.Values.proxy.nativeSidecar }}
      - {{- include "partials.proxy" $tree | indent 8 | trimPrefix (repeat 7 " ") }}
      {{- end }}
      - args:
        - proxy-injector
        - -log-level={{.Values.controllerLogLevel}}
        - -log-format={{.Values.controllerLogFormat}}
        - -linkerd-namespace={{.Release.Namespace}}
        - -enable-pprof={{.Values.enablePprof | default false}}
        {{- if or (.Values.proxyInjector).additionalEnv (.Values.proxyInjector).experimentalEnv }}
        env:
        {{- with (.Values.proxyInjector).additionalEnv }}
        {{- toYaml . | nindent 8 -}}
        {{- end }}
        {{- with (.Values.proxyInjector).experimentalEnv }}
        {{- toYaml . | nindent 8 -}}
        {{- end }}
        {{- end }}
        image: {{.Values.controllerImage}}:{{.Values.controllerImageVersion | default .Values.linkerdVersion}}
        imagePullPolicy: {{.Values.imagePullPolicy}}
        livenessProbe:
          httpGet:
            path: /ping
            port: 9995
          initialDelaySeconds: 10
        name: proxy-injector
        ports:
        - containerPort: 8443
          name: proxy-injector
        - containerPort: 9995
          name: admin-http
        readinessProbe:
          failureThreshold: 7
          httpGet:
            path: /ready
            port: 9995
        {{- if .Values.proxyInjectorResources -}}
        {{- include "partials.resources" .Values.proxyInjectorResources | nindent 8 }}
        {{- end }}
        securityContext:
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: {{.Values.controllerUID}}
          allowPrivilegeEscalation: false
          seccompProfile:
            type: RuntimeDefault
        volumeMounts:
        - mountPath: /var/run/linkerd/config
          name: config
        - mountPath: /var/run/linkerd/identity/trust-roots
          name: trust-roots
        - mountPath: /var/run/linkerd/tls
          name: tls
          readOnly: true
      initContainers:
      {{ if .Values.cniEnabled -}}
      - {{- include "partials.network-validator" $tree | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ else -}}
      {{- /*
        The controller needs to connect to the Kubernetes API. There's no reason
        to put the proxy in the way of that.
      */}}
      {{- $_ := set $tree.Values.proxyInit "ignoreOutboundPorts" .Values.proxyInit.kubeAPIServerPorts -}}
      - {{- include "partials.proxy-init" $tree | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ end -}}
      {{- if $tree.Values.proxy.nativeSidecar }}
        {{- $_ := set $tree.Values.proxy "startupProbeInitialDelaySeconds" 35 }}
        {{- $_ := set $tree.Values.proxy "startupProbePeriodSeconds" 5 }}
        {{- $_ := set $tree.Values.proxy "startupProbeFailureThreshold" 20 }}
      - {{- include "partials.proxy" $tree | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ end -}}
      {{- if .Values.priorityClassName -}}
      priorityClassName: {{ .Values.priorityClassName }}
      {{ end -}}
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: linkerd-proxy-injector
      volumes:
      - configMap:
          name: linkerd-config
        name: config
      - configMap:
          name: linkerd-identity-trust-roots
        name: trust-roots
      - name: tls
        secret:
          secretName: linkerd-proxy-injector-k8s-tls
      {{ if not .Values.cniEnabled -}}
      - {{- include "partials.proxyInit.volumes.xtables" . | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ end -}}
      {{if .Values.identity.serviceAccountTokenProjection -}}
      - {{- include "partials.proxy.volumes.service-account-token" . | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ end -}}
      - {{- include "partials.proxy.volumes.identity" . | indent 8 | trimPrefix (repeat 7 " ") }}
---
kind: Service
apiVersion: v1
metadata:
  name: linkerd-proxy-injector
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: proxy-injector
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
    config.linkerd.io/opaque-ports: "443"
spec:
  type: ClusterIP
  selector:
    linkerd.io/control-plane-component: proxy-injector
  ports:
  - name: proxy-injector
    port: 443
    targetPort: proxy-injector
{{- if .Values.enablePodDisruptionBudget }}
---
kind: PodDisruptionBudget
apiVersion: policy/v1
metadata:
  name: linkerd-proxy-injector
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: proxy-injector
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  maxUnavailable: {{ .Values.controller.podDisruptionBudget.maxUnavailable }}
  selector:
    matchLabels:
      linkerd.io/control-plane-component: proxy-injector
{{- end }}

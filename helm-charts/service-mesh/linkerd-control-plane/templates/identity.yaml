{{if .Values.identity -}}
---
###
### Identity Controller Service
###
{{ if and (.Values.identity.issuer) (eq .Values.identity.issuer.scheme "linkerd.io/tls") -}}
---
kind: Secret
apiVersion: v1
metadata:
  name: linkerd-identity-issuer
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
data:
  crt.pem: {{b64enc (required "Please provide the identity issuer certificate" .Values.identity.issuer.tls.crtPEM | trim)}}
  key.pem: {{b64enc (required "Please provide the identity issue private key" .Values.identity.issuer.tls.keyPEM | trim)}}
{{- end}}
{{ if not (.Values.identity.externalCA) -}}
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: linkerd-identity-trust-roots
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
data:
  ca-bundle.crt: |-{{.Values.identityTrustAnchorsPEM | trim | nindent 4}}
{{- end}}
---
kind: Service
apiVersion: v1
metadata:
  name: linkerd-identity
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  type: ClusterIP
  selector:
    linkerd.io/control-plane-component: identity
  ports:
  - name: grpc
    port: 8080
    targetPort: 8080
---
kind: Service
apiVersion: v1
metadata:
  name: linkerd-identity-headless
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  clusterIP: None
  selector:
    linkerd.io/control-plane-component: identity
  ports:
  - name: grpc
    port: 8080
    targetPort: 8080
{{- if .Values.enablePodDisruptionBudget }}
---
kind: PodDisruptionBudget
apiVersion: policy/v1
metadata:
  name: linkerd-identity
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  maxUnavailable: {{ .Values.controller.podDisruptionBudget.maxUnavailable }}
  selector:
    matchLabels:
      linkerd.io/control-plane-component: identity
{{- end }}
---
{{- $tree := deepCopy . }}
{{ $_ := set $tree.Values.proxy "workloadKind" "deployment" -}}
{{ $_ := set $tree.Values.proxy "component" "linkerd-identity" -}}
{{ $_ := set $tree.Values.proxy "waitBeforeExitSeconds" 0 -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    {{ include "partials.annotations.created-by" . }}
  labels:
    app.kubernetes.io/name: identity
    app.kubernetes.io/part-of: Linkerd
    app.kubernetes.io/version: {{.Values.linkerdVersion}}
    linkerd.io/control-plane-component: identity
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  name: linkerd-identity
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{.Values.controllerReplicas}}
  selector:
    matchLabels:
      linkerd.io/control-plane-component: identity
      linkerd.io/control-plane-ns: {{.Release.Namespace}}
      {{- include "partials.proxy.labels" $tree.Values.proxy | nindent 6}}
  {{- if .Values.deploymentStrategy }}
  strategy:
    {{- with .Values.deploymentStrategy }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  {{- end }}
  template:
    metadata:
      annotations:
        {{ include "partials.annotations.created-by" . }}
        {{- include "partials.proxy.annotations" . | nindent 8}}
        {{- with .Values.podAnnotations }}{{ toYaml . | trim | nindent 8 }}{{- end }}
        config.linkerd.io/default-inbound-policy: "all-unauthenticated"
      labels:
        linkerd.io/control-plane-component: identity
        linkerd.io/control-plane-ns: {{.Release.Namespace}}
        linkerd.io/workload-ns: {{.Release.Namespace}}
        {{- include "partials.proxy.labels" $tree.Values.proxy | nindent 8}}
        {{- with .Values.podLabels }}{{ toYaml . | trim | nindent 8 }}{{- end }}
    spec:
      {{- with .Values.runtimeClassName }}
      runtimeClassName: {{ . | quote }}
      {{- end }}
      {{- if .Values.tolerations -}}
      {{- include "linkerd.tolerations" . | nindent 6 }}
      {{- end -}}
      {{- include "linkerd.node-selector" . | nindent 6 }}
      {{- $_ := set $tree "component" "identity" -}}
      {{- include "linkerd.affinity" $tree | nindent 6 }}
      containers:
      - args:
        - identity
        - -log-level={{.Values.controllerLogLevel}}
        - -log-format={{.Values.controllerLogFormat}}
        - -controller-namespace={{.Release.Namespace}}
        - -identity-trust-domain={{.Values.identityTrustDomain | default .Values.clusterDomain}}
        - -identity-issuance-lifetime={{.Values.identity.issuer.issuanceLifetime}}
        - -identity-clock-skew-allowance={{.Values.identity.issuer.clockSkewAllowance}}
        - -identity-scheme={{.Values.identity.issuer.scheme}}
        - -enable-pprof={{.Values.enablePprof | default false}}
        - -kube-apiclient-qps={{.Values.identity.kubeAPI.clientQPS}}
        - -kube-apiclient-burst={{.Values.identity.kubeAPI.clientBurst}}
        {{- include "partials.linkerd.trace" . | nindent 8 -}}
        env:
        - name: LINKERD_DISABLED
          value: "linkerd-await cannot block the identity controller"
        {{- with (.Values.identity).additionalEnv }}
        {{- toYaml . | nindent 8 -}}
        {{- end }}
        {{- with (.Values.identity).experimentalEnv }}
        {{- toYaml . | nindent 8 -}}
        {{- end }}
        image: {{.Values.controllerImage}}:{{.Values.controllerImageVersion | default .Values.linkerdVersion}}
        imagePullPolicy: {{.Values.imagePullPolicy}}
        livenessProbe:
          httpGet:
            path: /ping
            port: 9990
          initialDelaySeconds: 10
        name: identity
        ports:
        - containerPort: 8080
          name: grpc
        - containerPort: 9990
          name: admin-http
        readinessProbe:
          failureThreshold: 7
          httpGet:
            path: /ready
            port: 9990
        {{- if .Values.identityResources -}}
        {{- include "partials.resources" .Values.identityResources | nindent 8 }}
        {{- end }}
        securityContext:
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: {{.Values.controllerUID}}
          allowPrivilegeEscalation: false
          seccompProfile:
            type: RuntimeDefault
        volumeMounts:
        - mountPath: /var/run/linkerd/identity/issuer
          name: identity-issuer
        - mountPath: /var/run/linkerd/identity/trust-roots/
          name: trust-roots
      {{- if not (empty .Values.identityProxyResources) }}
      {{- $r := merge .Values.identityProxyResources .Values.proxy.resources }}
      {{- $_ := set $tree.Values.proxy "resources" $r }}
      {{- end }}
      {{- $_ := set $tree.Values.proxy "await" false }}
      {{- $_ := set $tree.Values.proxy "loadTrustBundleFromConfigMap" true }}
      {{- $_ := set $tree.Values.proxy "podInboundPorts" "8080,9990" }}
      {{- $_ := set $tree.Values.proxy "nativeSidecar" false }}
      {{- /*
        The identity controller cannot discover policies, so we configure it with defaults that
        enforce TLS on the identity service.
      */}}
      {{- $_ := set $tree.Values.proxy "defaultInboundPolicy" "all-unauthenticated" }}
      {{- $_ := set $tree.Values.proxy "requireTLSOnInboundPorts" "8080" }}
      {{- $_ := set $tree.Values.proxy "capabilities" (dict "drop" (list "ALL")) }}
      {{- $_ := set $tree.Values.proxy "outboundDiscoveryCacheUnusedTimeout" "5s" }}
      {{- $_ := set $tree.Values.proxy "inboundDiscoveryCacheUnusedTimeout" "90s" }}
      - {{- include "partials.proxy" $tree | indent 8 | trimPrefix (repeat 7 " ") }}
      initContainers:
      {{ if .Values.cniEnabled -}}
      - {{- include "partials.network-validator" $tree | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ else -}}
      {{- /*
        The identity controller needs to connect to the Kubernetes API before the proxy is able to
        proxy requests, so we always skip these connections. The identity controller makes no other
        outbound connections (so it's not important to persist any other skip ports here)
      */}}
      {{- $_ := set $tree.Values.proxyInit "ignoreOutboundPorts" .Values.proxyInit.kubeAPIServerPorts -}}
      - {{- include "partials.proxy-init" $tree | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ end -}}
      {{- if .Values.priorityClassName -}}
      priorityClassName: {{ .Values.priorityClassName }}
      {{ end -}}
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: linkerd-identity
      volumes:
      - name: identity-issuer
        secret:
          secretName: linkerd-identity-issuer
      - configMap:
          name: linkerd-identity-trust-roots
        name: trust-roots
      {{ if not .Values.cniEnabled -}}
      - {{- include "partials.proxyInit.volumes.xtables" . | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ end -}}
      {{if .Values.identity.serviceAccountTokenProjection -}}
      - {{- include "partials.proxy.volumes.service-account-token" . | indent 8 | trimPrefix (repeat 7 " ") }}
      {{ end -}}
      - {{- include "partials.proxy.volumes.identity" . | indent 8 | trimPrefix (repeat 7 " ") }}
{{end -}}

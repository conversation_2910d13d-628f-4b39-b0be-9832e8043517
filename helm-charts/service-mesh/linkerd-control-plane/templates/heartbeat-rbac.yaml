{{ if not .Values.disableHeartBeat -}}
---
###
### Heartbeat RBAC
###
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: linkerd-heartbeat
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]
  resourceNames: ["linkerd-config"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: linkerd-heartbeat
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  kind: Role
  name: linkerd-heartbeat
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: linkerd-heartbeat
  namespace: {{.Release.Namespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: linkerd-heartbeat
  labels:
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["list"]
- apiGroups: ["linkerd.io"]
  resources: ["serviceprofiles"]
  verbs: ["list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: linkerd-heartbeat
  labels:
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  kind: ClusterRole
  name: linkerd-heartbeat
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: linkerd-heartbeat
  namespace: {{.Release.Namespace}}
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: linkerd-heartbeat
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: heartbeat
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}
{{- end }}

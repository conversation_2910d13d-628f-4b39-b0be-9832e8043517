---
###
### Proxy Injector RBAC
###
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-proxy-injector
  labels:
    linkerd.io/control-plane-component: proxy-injector
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
- apiGroups: [""]
  resources: ["namespaces", "replicationcontrollers"]
  verbs: ["list", "get", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["list", "watch"]
- apiGroups: ["extensions", "apps"]
  resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["extensions", "batch"]
  resources: ["cronjobs", "jobs"]
  verbs: ["list", "get", "watch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-proxy-injector
  labels:
    linkerd.io/control-plane-component: proxy-injector
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
subjects:
- kind: ServiceAccount
  name: linkerd-proxy-injector
  namespace: {{.Release.Namespace}}
  apiGroup: ""
roleRef:
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-proxy-injector
  apiGroup: rbac.authorization.k8s.io
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: linkerd-proxy-injector
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: proxy-injector
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}
---
{{- $host := printf "linkerd-proxy-injector.%s.svc" .Release.Namespace }}
{{- $ca := genSelfSignedCert $host (list) (list $host) 365 }}
{{- if (not .Values.proxyInjector.externalSecret) }}
kind: Secret
apiVersion: v1
metadata:
  name: linkerd-proxy-injector-k8s-tls
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/control-plane-component: proxy-injector
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
type: kubernetes.io/tls
data:
  tls.crt: {{ ternary (b64enc (trim $ca.Cert)) (b64enc (trim .Values.proxyInjector.crtPEM)) (empty .Values.proxyInjector.crtPEM) }}
  tls.key: {{ ternary (b64enc (trim $ca.Key)) (b64enc (trim .Values.proxyInjector.keyPEM)) (empty .Values.proxyInjector.keyPEM) }}
---
{{- end }}
{{- include "linkerd.webhook.validation" .Values.proxyInjector }}
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: linkerd-proxy-injector-webhook-config
  {{- if or (.Values.proxyInjector.injectCaFrom) (.Values.proxyInjector.injectCaFromSecret) }}
  annotations:
  {{- if .Values.proxyInjector.injectCaFrom }}
    cert-manager.io/inject-ca-from: {{ .Values.proxyInjector.injectCaFrom }}
  {{- end }}
  {{- if .Values.proxyInjector.injectCaFromSecret }}
    cert-manager.io/inject-ca-from-secret: {{ .Values.proxyInjector.injectCaFromSecret }}
  {{- end }}
  {{- end }}
  labels:
    linkerd.io/control-plane-component: proxy-injector
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
webhooks:
- name: linkerd-proxy-injector.linkerd.io
  namespaceSelector:
    {{- toYaml .Values.proxyInjector.namespaceSelector | trim | nindent 4 }}
  objectSelector:
    {{- toYaml .Values.proxyInjector.objectSelector | trim | nindent 4 }}
  clientConfig:
    service:
      name: linkerd-proxy-injector
      namespace: {{ .Release.Namespace }}
      path: "/"
    {{- if and (empty .Values.proxyInjector.injectCaFrom) (empty .Values.proxyInjector.injectCaFromSecret) }}
    caBundle: {{ ternary (b64enc (trim $ca.Cert)) (b64enc (trim .Values.proxyInjector.caBundle)) (empty .Values.proxyInjector.caBundle) }}
    {{- end }}
  failurePolicy: {{.Values.webhookFailurePolicy}}
  admissionReviewVersions: ["v1", "v1beta1"]
  rules:
  - operations: [ "CREATE" ]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods", "services"]
    scope: "Namespaced"
  sideEffects: None
  timeoutSeconds: {{ .Values.proxyInjector.timeoutSeconds | default 10 }}

# Default values for linkerd.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Fields that should be common with the core control plane

# -- control plane version. See Proxy section for proxy version
linkerdVersion: edge-24.3.5
# -- Kubernetes DNS Domain name to use
clusterDomain: cluster.local
# -- Additional labels to add to all pods
podLabels: {}
# -- Labels to apply to all resources
commonLabels: {}
# -- Trust domain used for identity
# @default -- clusterDomain
identityTrustDomain: ""

# -- Docker registry for all viz components
defaultRegistry: cr.l5d.io/linkerd
# -- Docker imagePullPolicy for all viz components
defaultImagePullPolicy: IfNotPresent
# -- Log level for all the viz components
defaultLogLevel: info
# -- Log format (`plain` or `json`) for all the viz components.
defaultLogFormat: plain
# -- UID for all the viz components
defaultUID: 2103

# -- Namespace of the Linkerd core control-plane install
linkerdNamespace: linkerd

# -- Default nodeSelector section, See the
# [K8S documentation](https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#nodeselector) for more information
nodeSelector: &default_node_selector
  kubernetes.io/os: linux

# -- For Private docker registries, authentication is needed.
#  Registry secrets are applied to the respective service accounts
imagePullSecrets: []
# - name: my-private-docker-registry-login-secret

# -- Default tolerations section, See the
# [K8S documentation](https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/)
# for more information
tolerations: &default_tolerations

# -- Enables Pod Anti Affinity logic to balance the placement of replicas
# across hosts and zones for High Availability.
# Enable this only when you have multiple replicas of components.
enablePodAntiAffinity: false

# -- enables the creation of pod disruption budgets for tap, tap-injector, web and metrics-api components
enablePodDisruptionBudget: false

# -- NodeAffinity section, See the
# [K8S documentation](https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity)
# for more information
# nodeAffinity:

# -- Creates a Job that adds necessary metadata to the extension's namespace
# during install; disable if lack of privileges require doing this manually
createNamespaceMetadataJob: true

# -- Create Roles and RoleBindings to associate this extension's
# ServiceAccounts to the control plane PSP resource. This requires that
# `enabledPSP` is set to true on the control plane install. Note PSP has been
# deprecated since k8s v1.21
enablePSP: false

# -- url of external prometheus instance
prometheusUrl: ""

# -- url of external jaeger instance
# Set this to `jaeger.linkerd-jaeger.svc.<clusterDomain>:16686` if you plan to use jaeger extension
jaegerUrl: ""

# metrics API configuration
metricsAPI:
  # -- Number of replicas of the metrics-api component
  replicas: 1
  # -- log level of the metrics-api component
  # @default -- defaultLogLevel
  logLevel: ""
  # -- log format of the metrics-api component
  # @default -- defaultLogFormat
  logFormat: ""
  image:
    # -- Docker registry for the metrics-api component
    # @default -- defaultRegistry
    registry: ""
    # -- Docker image name for the metrics-api component
    name: metrics-api
    # -- Docker image tag for the metrics-api component
    # @default -- linkerdVersion
    tag: ""
    # -- Pull policy for the metrics-api component
    # @default -- defaultImagePullPolicy
    pullPolicy: ""

  resources:
    cpu:
      # -- Maximum amount of CPU units that the metrics-api container can use
      limit:
      # -- Amount of CPU units that the metrics-api container requests
      request:
    memory:
      # -- Maximum amount of memory that metrics-api container can use
      limit:
      # -- Amount of memory that the metrics-api container requests
      request:
    ephemeral-storage:
      # -- Maximum amount of ephemeral storage that the metrics-api container can use
      limit: ""
      # -- Amount of ephemeral storage that the metrics-api container requests
      request: ""

  proxy:
    # -- If set, overrides default proxy resources for the proxy injected
    # into the metrics-api component
    # resources:

  # -- UID for the metrics-api resource
  UID:

  # -- NodeSelector section, See the
  # [K8S documentation](https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#nodeselector) for more information
  nodeSelector: *default_node_selector
  # -- Tolerations section, See the
  # [K8S documentation](https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/)
  # for more information
  tolerations: *default_tolerations

  # -- metrics-api service configuration
  service:
    # -- Additional annotations to add to metrics-api service
    annotations: {}

# tap configuration
tap:
  # -- Number of tap component replicas
  replicas: 1
  # -- log level of the tap component
  # @default -- defaultLogLevel
  logLevel: ""
  # -- log format of the tap component
  # @default -- defaultLogFormat
  logFormat: ""
  image:
    # -- Docker registry for the tap instance
    # @default -- defaultRegistry
    registry: ""
    # -- Docker image name for the tap instance
    name: tap
    # -- Docker image tag for the tap instance
    # @default -- linkerdVersion
    tag: ""
    # -- Pull policy for the tap component
    # @default -- defaultImagePullPolicy
    pullPolicy: ""

  # -- Do not create a secret resource for the Tap component.
  # If this is set to `true`, the value `tap.caBundle` must be set
  # or the ca bundle must injected with cert-manager ca injector using
  # `tap.injectCaFrom` or `tap.injectCaFromSecret` (see below).
  externalSecret: false

  # -- Certificate for the Tap component. If not provided and not using an external secret
  # then Helm will generate one.
  crtPEM: |

  # -- Certificate key for Tap component. If not provided and not using an external secret
  # then Helm will generate one.
  keyPEM: |

  # -- Bundle of CA certificates for tap.
  # If not provided nor injected with cert-manager,
  # then Helm will use the certificate generated for `tap.crtPEM`.
  # If `tap.externalSecret` is set to true, this value, injectCaFrom, or
  # injectCaFromSecret must be set, as no certificate will be generated.
  # See the cert-manager [CA Injector Docs](https://cert-manager.io/docs/concepts/ca-injector) for more information.
  caBundle: |

  # -- Inject the CA bundle from a cert-manager Certificate.
  # See the cert-manager [CA Injector Docs](https://cert-manager.io/docs/concepts/ca-injector/#injecting-ca-data-from-a-certificate-resource)
  # for more information.
  injectCaFrom: ""

  # -- Inject the CA bundle from a Secret.
  # If set, the `cert-manager.io/inject-ca-from-secret` annotation will be added to the webhook.
  # The Secret must have the CA Bundle stored in the `ca.crt` key and have
  # the `cert-manager.io/allow-direct-injection` annotation set to `true`.
  # See the cert-manager [CA Injector Docs](https://cert-manager.io/docs/concepts/ca-injector/#injecting-ca-data-from-a-secret-resource)
  # for more information.
  injectCaFromSecret: ""

  resources:
    cpu:
      # -- Maximum amount of CPU units that the tap container can use
      limit:
      # -- Amount of CPU units that the tap container requests
      request:
    memory:
      # -- Maximum amount of memory that tap container can use
      limit:
      # -- Amount of memory that the tap container requests
      request:
    ephemeral-storage:
      # -- Maximum amount of ephemeral storage that the tap container can use
      limit: ""
      # -- Amount of ephemeral storage that the tap container requests
      request: ""

  # -- List of headers that will be ignored for Linkerd Tap
  ignoreHeaders: []

  proxy:
    # -- If set, overrides default proxy resources for the proxy injected
    # into the tap component
    # resources:

  # -- UID for the dashboard resource
  UID:

  # -- tap service configuration
  service:
    # -- Additional annotations to add to tap service
    annotations: {}

# tapInjector configuration
tapInjector:
  # -- Number of replicas of tapInjector
  replicas: 1
  # -- log level of the tapInjector
  # @default -- defaultLogLevel
  logLevel: ""
  # -- log format of the tapInjector component
  # @default -- defaultLogFormat
  logFormat: ""
  image:
    # -- Docker registry for the tapInjector instance
    # @default -- defaultRegistry
    registry: ""
    # -- Docker image name for the tapInjector instance
    name: tap
    # -- Docker image tag for the tapInjector instance
    # @default -- linkerdVersion
    tag: ""
    # -- Pull policy for the tapInjector component
    # @default -- defaultImagePullPolicy
    pullPolicy: ""

  # -- Namespace selector used by admission webhook.
  namespaceSelector:
    matchExpressions:
    - key: kubernetes.io/metadata.name
      operator: NotIn
      values:
      - kube-system
  objectSelector:
    # matchLabels:
    #   foo: bar
  # -- UID for the tapInjector resource
  UID:
  failurePolicy: Ignore
  resources:
    cpu:
      # -- Maximum amount of CPU units that the tapInjector container can use
      limit:
      # -- Amount of CPU units that the tapInjector container requests
      request:
    memory:
      # -- Maximum amount of memory that tapInjector container can use
      limit:
      # -- Amount of memory that the tapInjector container requests
      request:
    ephemeral-storage:
      # -- Maximum amount of ephemeral storage that the tapInjector container can use
      limit: ""
      # -- Amount of ephemeral storage that the tapInjector container requests
      request: ""
  proxy:
    # -- If set, overrides default proxy resources for the proxy injected
    # into the tapInjector component
    # resources:

  # -- Do not create a secret resource for the tapInjector webhook.
  # If this is set to `true`, the value `tapInjector.caBundle` must be set
  # or the ca bundle must injected with cert-manager ca injector using
  # `tapInjector.injectCaFrom` or `tapInjector.injectCaFromSecret` (see below).
  externalSecret: false

  # -- Certificate for the tapInjector. If not provided and not using an external secret
  # then Helm will generate one.
  crtPEM: |

  # -- Certificate key for the tapInjector. If not provided and not using an external secret
  # then Helm will generate one.
  keyPEM: |

  # -- Bundle of CA certificates for the tapInjector.
  # If not provided nor injected with cert-manager,
  # then Helm will use the certificate generated for `tapInjector.crtPEM`.
  # If `tapInjector.externalSecret` is set to true, this value, injectCaFrom, or
  # injectCaFromSecret must be set, as no certificate will be generated.
  # See the cert-manager [CA Injector Docs](https://cert-manager.io/docs/concepts/ca-injector) for more information.
  caBundle: |

  # -- Inject the CA bundle from a cert-manager Certificate.
  # See the cert-manager [CA Injector Docs](https://cert-manager.io/docs/concepts/ca-injector/#injecting-ca-data-from-a-certificate-resource)
  # for more information.
  injectCaFrom: ""

  # -- Inject the CA bundle from a Secret.
  # If set, the `cert-manager.io/inject-ca-from-secret` annotation will be added to the webhook.
  # The Secret must have the CA Bundle stored in the `ca.crt` key and have
  # the `cert-manager.io/allow-direct-injection` annotation set to `true`.
  # See the cert-manager [CA Injector Docs](https://cert-manager.io/docs/concepts/ca-injector/#injecting-ca-data-from-a-secret-resource)
  # for more information.
  injectCaFromSecret: ""

  # -- tap service configuration
  service:
    # -- Additional annotations to add to tapInjector service
    annotations: {}

# web dashboard configuration
dashboard:
  # -- Number of replicas of dashboard
  replicas: 1
  # -- log level of the dashboard component
  # @default -- defaultLogLevel
  logLevel: ""
  # -- log format of the dashboard component
  # @default -- defaultLogFormat
  logFormat: ""
  image:
    # -- Docker registry for the web instance
    # @default -- defaultRegistry
    registry: ""
    # -- Docker image name for the web instance
    name: web
    # -- Docker image tag for the web instance
    # @default -- linkerdVersion
    tag: ""
    # -- Pull policy for the  web component
    # @default -- defaultImagePullPolicy
    pullPolicy: ""

  # -- UID for the dashboard resource
  UID:

  # -- Restrict the Linkerd Dashboard's default privileges to disallow Tap and Check
  restrictPrivileges: false

  # -- Host header validation regex for the dashboard. See the [Linkerd
  # documentation](https://linkerd.io/2/tasks/exposing-dashboard) for more
  # information
  enforcedHostRegexp: ""
  resources:
    cpu:
      # -- Maximum amount of CPU units that the web container can use
      limit:
      # -- Amount of CPU units that the web container requests
      request:
    memory:
      # -- Maximum amount of memory that web container can use
      limit:
      # -- Amount of memory that the web container requests
      request:
    ephemeral-storage:
      # -- Maximum amount of ephemeral storage that the web container can use
      limit: ""
      # -- Amount of ephemeral storage that the web container requests
      request: ""

  proxy:
    # -- If set, overrides default proxy resources for the proxy injected
    # into the dashboard component
    # resources:

  # -- dashboard service configuration
  service:
    # -- Additional annotations to add to dashboard service
    annotations: {}

namespaceMetadata:
  image:
    # -- Docker registry for the namespace-metadata instance
    # @default -- defaultRegistry
    registry: ""
    # -- Docker image name for the namespace-metadata instance
    name: extension-init
    # -- Docker image tag for the namespace-metadata instance
    tag: v0.1.0
    # -- Pull policy for the namespace-metadata instance
    # @default -- defaultImagePullPolicy
    pullPolicy: ""

  # -- NodeSelector section, See the
  # [K8S documentation](https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#nodeselector) for more information
  nodeSelector: *default_node_selector
  # -- Tolerations section, See the
  # [K8S documentation](https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/)
  # for more information
  tolerations: *default_tolerations

grafana:
  # -- url of an in-cluster Grafana instance with reverse proxy configured, used by the
  # Linkerd viz web dashboard to provide direct links to specific Grafana
  # dashboards. Cannot be set if grafana.externalUrl is set. See the [Linkerd
  # documentation](https://linkerd.io/2/tasks/grafana) for more information
  url:
  # -- url of a Grafana instance hosted off-cluster. Cannot be set if
  # grafana.url is set. The reverse proxy will not be used for this URL.
  externalUrl:
  # -- prefix for Grafana dashboard UID's, used when grafana.externalUrl is
  # set.
  uidPrefix:

prometheus:
  # -- toggle field to enable or disable prometheus
  enabled: true
  image:
    # -- Docker registry for the prometheus instance
    registry: prom
    # -- Docker image name for the prometheus instance
    name: prometheus
    # -- Docker image tag for the prometheus instance
    tag: v2.48.1
    # -- Pull policy for the prometheus instance
    # @default -- defaultImagePullPolicy
    pullPolicy: ""

  # -- log level of the prometheus instance
  # @default -- defaultLogLevel
  logLevel: ""
  # -- log format (plain, json) of the prometheus instance
  # @default -- defaultLogLevel
  logFormat: ""
  # -- Command line options for Prometheus binary
  args:
    storage.tsdb.path: /data
    storage.tsdb.retention.time: 6h
    config.file: /etc/prometheus/prometheus.yml
  # -- The global configuration specifies parameters that are valid in all other
  # configuration contexts.
  globalConfig:
    scrape_interval: 10s
    scrape_timeout: 10s
    evaluation_interval: 10s

  # -- annotations for the prometheus pod
  podAnnotations: {}

  # -- Alert relabeling is applied to alerts before they are sent to the
  # Alertmanager.
  alertRelabelConfigs:
  # Ex:
  # - action: labeldrop
  #   regex: prometheus_replica

  # -- Alertmanager instances the Prometheus server sends alerts to configured via
  # the static_configs parameter.
  alertmanagers:
  # Ex:
  # - scheme: http
  #   static_configs:
  #   - targets:
  #     - "alertmanager.linkerd.svc:9093"

  # -- Allows transparently sending samples to an endpoint. Mostly used for long
  # term storage.
  remoteWrite:

  # -- Alerting/recording rule ConfigMap mounts (sub-path names must end in
  # ´_rules.yml´ or ´_rules.yaml´)
  ruleConfigMapMounts:
  # Ex:
  # - name: alerting-rules
  #   subPath: alerting_rules.yml
  #   configMap: linkerd-prometheus-rules
  # - name: recording-rules
  #   subPath: recording_rules.yml
  #   configMap: linkerd-prometheus-rules

  # -- A scrapeConfigs section specifies a set of targets and parameters
  # describing how to scrape them.
  scrapeConfigs:
  # Ex:
  # - job_name: 'kubernetes-nodes'
  #   scheme: https
  #   tls_config:
  #     ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
  #   bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
  #   kubernetes_sd_configs:
  #   - role: node
  #   relabel_configs:
  #   - action: labelmap
  #     regex: __meta_kubernetes_node_label_(.+)

  # -- A sidecarContainers section specifies a list of secondary containers to run
  # in the prometheus pod e.g. to export data to non-prometheus systems
  sidecarContainers:
  # Ex:
  # - name: sidecar
  #   image: gcr.io/myproject/stackdriver-prometheus-sidecar
  #   imagePullPolicy: Always
  #   command:
  #   - /bin/sh
  #   - -c
  #   - |
  #     exec /bin/stackdriver-prometheus-sidecar \
  #       --stackdriver.project-id=myproject \
  #       --stackdriver.kubernetes.location=us-central1 \
  #       --stackdriver.kubernetes.cluster-name=mycluster \
  #       --prometheus.wal-directory=/data/wal \
  #       --log.level=info
  #   volumeMounts:
  #   - mountPath: /data
  #     name: data
  #   ports:
  #   - name: foo
  #     containerPort: 9091
  #     protocol: TCP
  ### WARNING: persistence is experimental and has not been tested/vetted by the Linkerd team.
  ### As such, please refer to https://linkerd.io/2/tasks/exporting-metrics/ for the recommended approach to metrics data retention.
  # if enabled, creates a persistent volume claim for prometheus data
  # https://kubernetes.io/docs/concepts/storage/persistent-volumes/#persistentvolumeclaims
  #persistence:
  # -- Storage class used to create prometheus data PV.
  #  storageClass:
  # -- PVC access mode.
  #  accessMode:
  # -- Prometheus data volume size.
  #  size:

  resources:
    cpu:
      # -- Maximum amount of CPU units that the prometheus container can use
      limit:
      # -- Amount of CPU units that the prometheus container requests
      request:
    memory:
      # -- Maximum amount of memory that prometheus container can use
      limit:
      # -- Amount of memory that the prometheus container requests
      request:
    ephemeral-storage:
      # -- Maximum amount of ephemeral storage that the prometheus container can use
      limit: ""
      # -- Amount of ephemeral storage that the prometheus container requests
      request: ""

  proxy:
    # -- If set, overrides default proxy resources for the proxy injected
    # into the prometheus component
    # resources:

  # -- NodeSelector section, See the
  # [K8S documentation](https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#nodeselector) for more information
  nodeSelector: *default_node_selector
  # -- Tolerations section, See the
  # [K8S documentation](https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/)
  # for more information
  tolerations: *default_tolerations

---
###
### Tap RBAC
###
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-tap
  labels:
    linkerd.io/extension: viz
    component: tap
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: [""]
  resources: ["pods", "services", "replicationcontrollers", "namespaces", "nodes"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["extensions", "apps"]
  resources: ["daemonsets", "deployments", "replicasets", "statefulsets"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["extensions", "batch"]
  resources: ["cronjobs", "jobs"]
  verbs: ["list" , "get", "watch"]
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-tap-admin
  labels:
    linkerd.io/extension: viz
    component: tap
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["list"]
- apiGroups: ["tap.linkerd.io"]
  resources: ["*"]
  verbs: ["watch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-tap
  labels:
    linkerd.io/extension: viz
    component: tap
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-tap
subjects:
- kind: ServiceAccount
  name: tap
  namespace: {{.Release.Namespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: linkerd-{{.Release.Namespace}}-tap-auth-delegator
  labels:
    linkerd.io/extension: viz
    component: tap
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:auth-delegator
subjects:
- kind: ServiceAccount
  name: tap
  namespace: {{.Release.Namespace}}
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: tap
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: tap
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: linkerd-{{.Release.Namespace}}-tap-auth-reader
  namespace: kube-system
  labels:
    linkerd.io/extension: viz
    component: tap
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: extension-apiserver-authentication-reader
subjects:
- kind: ServiceAccount
  name: tap
  namespace: {{.Release.Namespace}}
---
{{- $host := printf "tap.%s.svc" .Release.Namespace }}
{{- $ca := genSelfSignedCert $host (list) (list $host) 365 }}
{{- if (not .Values.tap.externalSecret) }}
kind: Secret
apiVersion: v1
metadata:
  name: tap-k8s-tls
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: tap
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
type: kubernetes.io/tls
data:
  tls.crt: {{ ternary (b64enc (trim $ca.Cert)) (b64enc (trim .Values.tap.crtPEM)) (empty .Values.tap.crtPEM) }}
  tls.key: {{ ternary (b64enc (trim $ca.Key)) (b64enc (trim .Values.tap.keyPEM)) (empty .Values.tap.keyPEM) }}
---
{{- end }}
{{- include "linkerd.webhook.validation" .Values.tap }}
apiVersion: apiregistration.k8s.io/v1
kind: APIService
metadata:
  name: v1alpha1.tap.linkerd.io
  {{- if or (.Values.tap.injectCaFrom) (.Values.tap.injectCaFromSecret) }}
  annotations:
  {{- if .Values.tap.injectCaFrom }}
    cert-manager.io/inject-ca-from: {{ .Values.tap.injectCaFrom }}
  {{- end }}
  {{- if .Values.tap.injectCaFromSecret }}
    cert-manager.io/inject-ca-from-secret: {{ .Values.tap.injectCaFromSecret }}
  {{- end }}
  {{- end }}
  labels:
    linkerd.io/extension: viz
    component: tap
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
spec:
  group: tap.linkerd.io
  version: v1alpha1
  groupPriorityMinimum: 1000
  versionPriority: 100
  service:
    name: tap
    namespace: {{.Release.Namespace}}
  {{- if and (empty .Values.tap.injectCaFrom) (empty .Values.tap.injectCaFromSecret) }}
  caBundle: {{ ternary (b64enc (trim $ca.Cert)) (b64enc (trim .Values.tap.caBundle)) (empty .Values.tap.caBundle) }}
  {{- end }}

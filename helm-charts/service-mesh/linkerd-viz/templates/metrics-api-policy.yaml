---
apiVersion: policy.linkerd.io/v1beta2
kind: Server
metadata:
  namespace: {{ .Release.Namespace }}
  name: metrics-api
  labels:
    linkerd.io/extension: viz
    component: metrics-api
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  podSelector:
    matchLabels:
      linkerd.io/extension: viz
      component: metrics-api
  port: http
  proxyProtocol: HTTP/1
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  namespace: {{ .Release.Namespace }}
  name: metrics-api
  labels:
    linkerd.io/extension: viz
    component: metrics-api
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: Server
    name: metrics-api
  requiredAuthenticationRefs:
  - group: policy.linkerd.io
    kind: MeshTLSAuthentication
    name: metrics-api-web
---
apiVersion: policy.linkerd.io/v1alpha1
kind: MeshTLSAuthentication
metadata:
  namespace: {{ .Release.Namespace }}
  name: metrics-api-web
  labels:
    linkerd.io/extension: viz
    component: metrics-api
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  identityRefs:
  - kind: ServiceAccount
    name: web

---
apiVersion: policy.linkerd.io/v1beta2
kind: Server
metadata:
  namespace: {{ .Release.Namespace }}
  name: tap-api
  labels:
    linkerd.io/extension: viz
    component: tap
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  podSelector:
    matchLabels:
      linkerd.io/extension: viz
      component: tap
  port: apiserver
  proxyProtocol: TLS
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  namespace: {{ .Release.Namespace }}
  name: tap
  labels:
    linkerd.io/extension: viz
    component: tap
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: Server
    name: tap-api
  requiredAuthenticationRefs:
  - group: policy.linkerd.io
    kind: NetworkAuthentication
    name: kube-api-server

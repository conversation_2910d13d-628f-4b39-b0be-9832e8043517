---
apiVersion: linkerd.io/v1alpha2
kind: ServiceProfile
metadata:
  name: metrics-api.{{.Release.Namespace}}.svc.{{.Values.clusterDomain}}
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
spec:
  routes:
  - name: POST /api/v1/StatSummary
    condition:
      method: POST
      pathRegex: /api/v1/StatSummary
  - name: POST /api/v1/TopRoutes
    condition:
      method: POST
      pathRegex: /api/v1/TopRoutes
  - name: POST /api/v1/ListPods
    condition:
      method: POST
      pathRegex: /api/v1/ListPods
  - name: POST /api/v1/ListServices
    condition:
      method: POST
      pathRegex: /api/v1/ListServices
  - name: POST /api/v1/SelfCheck
    condition:
      method: POST
      pathRegex: /api/v1/SelfCheck
  - name: POST /api/v1/Gateways
    condition:
      method: POST
      pathRegex: /api/v1/Gateways
  - name: POST /api/v1/Edges
    condition:
      method: POST
      pathRegex: /api/v1/Edges
{{ if .Values.prometheus.enabled -}}
---
apiVersion: linkerd.io/v1alpha2
kind: ServiceProfile
metadata:
  name: prometheus.{{.Release.Namespace}}.svc.{{.Values.clusterDomain}}
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
spec:
  routes:
  - name: POST /api/v1/query
    condition:
      method: POST
      pathRegex: /api/v1/query
  - name: GET /api/v1/query_range
    condition:
      method: GET
      pathRegex: /api/v1/query_range
  - name: GET /api/v1/series
    condition:
      method: GET
      pathRegex: /api/v1/series
{{ end -}}

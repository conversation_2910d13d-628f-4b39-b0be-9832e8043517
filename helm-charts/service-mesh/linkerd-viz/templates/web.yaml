---
###
### Web
###
kind: Service
apiVersion: v1
metadata:
  name: web
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: web
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
    {{ with .Values.dashboard.service.annotations }}{{ toYaml . | trim | nindent 4 }}{{ end }}
    linkerd.io/inject: enabled
spec:
  type: ClusterIP
  selector:
    linkerd.io/extension: viz
    component: web
  ports:
  - name: http
    port: 8084
    targetPort: 8084
  - name: admin-http
    port: 9994
    targetPort: 9994
---
{{- $tree := deepCopy . }}
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    {{ include "partials.annotations.created-by" . }}
    linkerd.io/inject: enabled
    config.linkerd.io/proxy-await: "enabled"
  labels:
    linkerd.io/extension: viz
    app.kubernetes.io/name: web
    app.kubernetes.io/part-of: Linkerd
    app.kubernetes.io/version: {{default .Values.linkerdVersion .Values.cliVersion}}
    component: web
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  name: web
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{.Values.dashboard.replicas}}
  selector:
    matchLabels:
      linkerd.io/extension: viz
      component: web
      namespace: {{.Release.Namespace}}
  template:
    metadata:
      annotations:
        {{ include "partials.annotations.created-by" . }}
        {{- with .Values.dashboard.proxy }}
        {{- include "partials.proxy.config.annotations" .resources | nindent 8 }}
        {{- end }}
        {{- with .Values.podAnnotations }}{{ toYaml . | trim | nindent 8 }}{{- end }}
        linkerd.io/inject: enabled
        config.alpha.linkerd.io/proxy-wait-before-exit-seconds: "0"
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
      labels:
        linkerd.io/extension: viz
        component: web
        namespace: {{.Release.Namespace}}
        {{- with .Values.podLabels }}{{ toYaml . | trim | nindent 8 }}{{- end }}
    spec:
      {{- if .Values.tolerations -}}
      {{- include "linkerd.tolerations" . | nindent 6 }}
      {{- end -}}
      {{- include "linkerd.node-selector" . | nindent 6 }}
      {{- $_ := set $tree "component" "web" -}}
      {{- $_ := set $tree "label" "component" -}}
      {{- include "linkerd.affinity" $tree | nindent 6 }}
      containers:
      - args:
        - -linkerd-metrics-api-addr=metrics-api.{{.Release.Namespace}}.svc.{{.Values.clusterDomain}}:8085
        - -cluster-domain={{.Values.clusterDomain}}
        {{- if and .Values.grafana.url .Values.grafana.externalUrl }}
        {{ fail "Cannot set both grafana.url (on-cluster Grafana) and grafana.externalUrl (off-cluster Grafana)"}}
        {{- end}}
        {{- if .Values.grafana.url }}
        - -grafana-addr={{.Values.grafana.url}}
        {{- end}}
        {{- if .Values.grafana.externalUrl }}
        - -grafana-external-addr={{.Values.grafana.externalUrl}}
        {{- end}}
        {{- if .Values.grafana.uidPrefix }}
        - -grafana-prefix={{.Values.grafana.uidPrefix}}
        {{- end}}
        {{- if .Values.jaegerUrl }}
        - -jaeger-addr={{.Values.jaegerUrl}}
        {{- end}}
        - -controller-namespace={{.Values.linkerdNamespace}}
        - -log-level={{.Values.dashboard.logLevel | default .Values.defaultLogLevel}}
        - -log-format={{.Values.dashboard.logFormat | default .Values.defaultLogFormat}}
        {{- if .Values.dashboard.enforcedHostRegexp }}
        - -enforced-host={{.Values.dashboard.enforcedHostRegexp}}
        {{- else -}}
        {{- $hostFull := replace "." "\\." (printf "web.%s.svc.%s" .Release.Namespace .Values.clusterDomain) }}
        {{- $hostAbbrev := replace "." "\\." (printf "web.%s.svc" .Release.Namespace) }}
        - -enforced-host=^(localhost|127\.0\.0\.1|{{ $hostFull }}|{{ $hostAbbrev }}|\[::1\])(:\d+)?$
        {{- end}}
        - -enable-pprof={{.Values.enablePprof | default false}}
        image: {{.Values.dashboard.image.registry | default .Values.defaultRegistry}}/{{.Values.dashboard.image.name}}:{{.Values.dashboard.image.tag | default .Values.linkerdVersion}}
        imagePullPolicy: {{.Values.dashboard.image.pullPolicy | default .Values.defaultImagePullPolicy}}
        livenessProbe:
          httpGet:
            path: /ping
            port: 9994
          initialDelaySeconds: 10
        name: web
        ports:
        - containerPort: 8084
          name: http
        - containerPort: 9994
          name: admin-http
        readinessProbe:
          failureThreshold: 7
          httpGet:
            path: /ready
            port: 9994
        {{- if .Values.dashboard.resources -}}
        {{- include "partials.resources" .Values.dashboard.resources | nindent 8 }}
        {{- end }}
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: {{.Values.dashboard.UID | default .Values.defaultUID}}
          seccompProfile:
            type: RuntimeDefault
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: web
{{- if and .Values.enablePodDisruptionBudget (gt (int .Values.dashboard.replicas) 1) }}
---
kind: PodDisruptionBudget
apiVersion: policy/v1
metadata:
  name: web
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: web
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      linkerd.io/extension: viz
      component: web
{{- end }}

---
###
### Tap
###
kind: Service
apiVersion: v1
metadata:
  name: tap
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: tap
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
    {{- with .Values.tap.service.annotations }}{{ toYaml . | trim | nindent 4 }}{{- end }}
    linkerd.io/inject: enabled
spec:
  type: ClusterIP
  selector:
    linkerd.io/extension: viz
    component: tap
  ports:
  - name: grpc
    port: 8088
    targetPort: 8088
  - name: apiserver
    port: 443
    targetPort: apiserver
---
{{- $tree := deepCopy . }}
kind: Deployment
apiVersion: apps/v1
metadata:
  annotations:
    {{ include "partials.annotations.created-by" . }}
    linkerd.io/inject: enabled
    config.linkerd.io/proxy-await: "enabled"
  labels:
    linkerd.io/extension: viz
    app.kubernetes.io/name: tap
    app.kubernetes.io/part-of: Linkerd
    app.kubernetes.io/version: {{default .Values.linkerdVersion .Values.cliVersion}}
    component: tap
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  name: tap
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{.Values.tap.replicas}}
  selector:
    matchLabels:
      linkerd.io/extension: viz
      component: tap
      namespace: {{.Release.Namespace}}
  {{- if .Values.enablePodAntiAffinity }}
  strategy:
    rollingUpdate:
      maxUnavailable: 1
  {{- end }}
  template:
    metadata:
      annotations:
        {{- if empty .Values.cliVersion }}
        checksum/config: {{ include (print $.Template.BasePath "/tap-rbac.yaml") . | sha256sum }}
        {{- end }}
        {{ include "partials.annotations.created-by" . }}
        {{- with .Values.tap.proxy }}
        {{- include "partials.proxy.config.annotations" .resources | nindent 8 }}
        {{- end }}
        {{- with .Values.podAnnotations }}{{ toYaml . | trim | nindent 8 }}{{- end }}
        linkerd.io/inject: enabled
        config.alpha.linkerd.io/proxy-wait-before-exit-seconds: "0"
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
      labels:
        linkerd.io/extension: viz
        component: tap
        namespace: {{.Release.Namespace}}
        {{- with .Values.podLabels }}{{ toYaml . | trim | nindent 8 }}{{- end }}
    spec:
      {{- if .Values.tolerations -}}
      {{- include "linkerd.tolerations" . | nindent 6 }}
      {{- end -}}
      {{- include "linkerd.node-selector" . | nindent 6 }}
      {{- $_ := set $tree "component" "tap" -}}
      {{- $_ := set $tree "label" "component" -}}
      {{- include "linkerd.affinity" $tree | nindent 6 }}
      containers:
      - args:
        - api
        - -api-namespace={{.Values.linkerdNamespace}}
        - -log-level={{.Values.tap.logLevel | default .Values.defaultLogLevel}}
        - -log-format={{.Values.tap.logFormat | default .Values.defaultLogFormat}}
        - -identity-trust-domain={{.Values.identityTrustDomain | default .Values.clusterDomain}}
        - -enable-pprof={{.Values.enablePprof | default false}}
        {{- if .Values.tap.ignoreHeaders }}
        - -ignore-headers={{ .Values.tap.ignoreHeaders | join "," }}
        {{- end }}
        image: {{.Values.tap.image.registry | default .Values.defaultRegistry}}/{{.Values.tap.image.name}}:{{.Values.tap.image.tag | default .Values.linkerdVersion}}
        imagePullPolicy: {{.Values.tap.image.pullPolicy | default .Values.defaultImagePullPolicy}}
        livenessProbe:
          httpGet:
            path: /ping
            port: 9998
          initialDelaySeconds: 10
        name: tap
        ports:
        - containerPort: 8088
          name: grpc
        - containerPort: 8089
          name: apiserver
        - containerPort: 9998
          name: admin-http
        readinessProbe:
          failureThreshold: 7
          httpGet:
            path: /ready
            port: 9998
        {{- if .Values.tap.resources -}}
        {{- include "partials.resources" .Values.tap.resources | nindent 8 }}
        {{- end }}
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: {{.Values.tap.UID | default .Values.defaultUID}}
          seccompProfile:
            type: RuntimeDefault
        volumeMounts:
        - mountPath: /var/run/linkerd/tls
          name: tls
          readOnly: true
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: tap
      volumes:
      - name: tls
        secret:
          secretName: tap-k8s-tls
{{- if and .Values.enablePodDisruptionBudget (gt (int .Values.tap.replicas) 1) }}
---
kind: PodDisruptionBudget
apiVersion: policy/v1
metadata:
  name: tap
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: tap
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      linkerd.io/extension: viz
      component: tap
{{- end }}

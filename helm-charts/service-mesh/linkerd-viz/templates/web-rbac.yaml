---
###
### Web RBAC
###
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: web
  namespace: {{.Values.linkerdNamespace}}
  labels:
    linkerd.io/extension: viz
    component: web
    namespace: {{.Values.linkerdNamespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]
  resourceNames: ["linkerd-config"]
  {{- if not .Values.dashboard.restrictPrivileges }}
- apiGroups: [""]
  resources: ["namespaces", "configmaps"]
  verbs: ["get"]
- apiGroups: [""]
  resources: ["serviceaccounts", "pods"]
  verbs: ["list"]
- apiGroups: ["apps"]
  resources: ["replicasets"]
  verbs: ["list"]
  {{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: web
  namespace: {{.Values.linkerdNamespace}}
  labels:
    linkerd.io/extension: viz
    component: web
    namespace: {{.Values.linkerdNamespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  kind: Role
  name: web
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: web
  namespace: {{.Release.Namespace}}
---
{{- if not .Values.dashboard.restrictPrivileges }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: linkerd-{{.Release.Namespace}}-web-check
  labels:
    linkerd.io/extension: viz
    component: web
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: ["rbac.authorization.k8s.io"]
  resources: ["clusterroles", "clusterrolebindings"]
  verbs: ["list"]
- apiGroups: ["apiextensions.k8s.io"]
  resources: ["customresourcedefinitions"]
  verbs: ["list"]
- apiGroups: ["admissionregistration.k8s.io"]
  resources: ["mutatingwebhookconfigurations", "validatingwebhookconfigurations"]
  verbs: ["list"]
- apiGroups: ["linkerd.io"]
  resources: ["serviceprofiles"]
  verbs: ["list"]
- apiGroups: [""]
  resources: ["nodes", "pods", "services"]
  verbs: ["list"]
- apiGroups: ["apiregistration.k8s.io"]
  resources: ["apiservices"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: linkerd-{{.Release.Namespace}}-web-check
  labels:
    linkerd.io/extension: viz
    component: web
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-web-check
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: web
  namespace: {{.Release.Namespace}}
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-web-admin
  labels:
    linkerd.io/extension: viz
    component: web
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-tap-admin
subjects:
- kind: ServiceAccount
  name: web
  namespace: {{.Release.Namespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: linkerd-{{.Release.Namespace}}-web-api
  labels:
    linkerd.io/extension: viz
    component: web
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: linkerd-{{.Release.Namespace}}-web-api
  labels:
    linkerd.io/extension: viz
    component: web
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-web-api
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: web
  namespace: {{.Release.Namespace}}
---
{{- end}}
kind: ServiceAccount
apiVersion: v1
metadata:
  name: web
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: web
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}

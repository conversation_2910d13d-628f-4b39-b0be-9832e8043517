{{ if .Values.prometheus.enabled -}}
---
###
### Prometheus RBAC
###
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-prometheus
  labels:
    linkerd.io/extension: viz
    component: prometheus
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: [""]
  resources: ["nodes", "nodes/proxy", "pods"]
  verbs: ["get", "list", "watch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-prometheus
  labels:
    linkerd.io/extension: viz
    component: prometheus
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: {{.Release.Namespace}}
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: prometheus
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: prometheus
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}
{{ end -}}

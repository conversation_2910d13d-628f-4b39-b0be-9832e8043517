{{ if .Values.enablePSP -}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: psp
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: ['policy', 'extensions']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - linkerd-{{.Values.linkerdNamespace}}-control-plane
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: viz-psp
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    namespace: {{.Release.Namespace}}
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  kind: Role
  name: psp
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: tap
  namespace: {{.Release.Namespace}}
- kind: ServiceAccount
  name: web
  namespace: {{.Release.Namespace}}
{{ if .Values.prometheus.enabled -}}
- kind: ServiceAccount
  name: prometheus
  namespace: {{.Release.Namespace}}
{{ end -}}
- kind: ServiceAccount
  name: metrics-api
  namespace: {{.Release.Namespace}}
- kind: ServiceAccount
  name: tap-injector
  namespace: {{.Release.Namespace}}
- kind: ServiceAccount
  name: namespace-metadata
  namespace: {{.Release.Namespace}}
{{ end -}}

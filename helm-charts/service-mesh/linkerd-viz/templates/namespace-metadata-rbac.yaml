{{- if .Values.createNamespaceMetadataJob}}
kind: ServiceAccount
apiVersion: v1
metadata:
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
    "helm.sh/hook": post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: namespace-metadata
  namespace: {{.Release.Namespace}}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
    "helm.sh/hook": post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: namespace-metadata
  namespace: {{.Release.Namespace}}
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "patch"]
  resourceNames: ["{{.Release.Namespace}}"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
    "helm.sh/hook": post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: namespace-metadata
  namespace: {{.Release.Namespace}}
roleRef:
  kind: Role
  name: namespace-metadata
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: namespace-metadata
  namespace: {{.Release.Namespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  namespace: {{ .Values.linkerdNamespace }}
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
    "helm.sh/hook": post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: viz-namespace-metadata-linkerd-config
roleRef:
  kind: Role
  name: ext-namespace-metadata-linkerd-config
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: namespace-metadata
  namespace: {{.Release.Namespace}}
{{- end }}

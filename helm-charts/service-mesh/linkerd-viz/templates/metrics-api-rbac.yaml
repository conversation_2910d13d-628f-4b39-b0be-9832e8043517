---
###
### Metrics API RBAC
###
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-metrics-api
  labels:
    linkerd.io/extension: viz
    component: metrics-api
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
rules:
- apiGroups: ["extensions", "apps"]
  resources: ["daemonsets", "deployments", "replicasets", "statefulsets"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["extensions", "batch"]
  resources: ["cronjobs", "jobs"]
  verbs: ["list" , "get", "watch"]
- apiGroups: [""]
  resources: ["pods", "endpoints", "services", "replicationcontrollers", "namespaces"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["linkerd.io"]
  resources: ["serviceprofiles"]
  verbs: ["list", "get", "watch"]
- apiGroups: ["policy.linkerd.io"]
  resources: ["servers", "serverauthorizations", "authorizationpolicies", "httproutes"]
  verbs: ["list", "get"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: linkerd-{{.Release.Namespace}}-metrics-api
  labels:
    linkerd.io/extension: viz
    component: metrics-api
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: linkerd-{{.Release.Namespace}}-metrics-api
subjects:
- kind: ServiceAccount
  name: metrics-api
  namespace: {{.Release.Namespace}}
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: metrics-api
  namespace: {{ .Release.Namespace }}
  labels:
    linkerd.io/extension: viz
    component: metrics-api
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
{{- include "partials.image-pull-secrets" .Values.imagePullSecrets }}

---
apiVersion: policy.linkerd.io/v1alpha1
kind: NetworkAuthentication
metadata:
  namespace: {{ .Release.Namespace }}
  name: kubelet
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  # Ideally, this should be restricted to the actual set of IPs kubelet uses in
  # a cluster. This can't easily be discovered.
  networks:
  - cidr: "0.0.0.0/0"
  - cidr: "::/0"

---
apiVersion: policy.linkerd.io/v1beta2
kind: Server
metadata:
  namespace: {{ .Release.Namespace }}
  name: prometheus-admin
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  podSelector:
    matchLabels:
      linkerd.io/extension: viz
      component: prometheus
      namespace: {{.Release.Namespace}}
  port: admin-http
  proxyProtocol: HTTP/1
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  namespace: {{ .Release.Namespace }}
  name: prometheus-admin
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: Server
    name: prometheus-admin
  requiredAuthenticationRefs:
    - kind: ServiceAccount
      name: metrics-api
      namespace: {{.Release.Namespace}}

---
apiVersion: policy.linkerd.io/v1beta2
kind: Server
metadata:
  namespace: {{ .Release.Namespace }}
  name: tap-injector-webhook
  labels:
    linkerd.io/extension: viz
    component: tap-injector
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  podSelector:
    matchLabels:
      linkerd.io/extension: viz
      component: tap-injector
  port: tap-injector
  proxyProtocol: TLS
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  namespace: {{ .Release.Namespace }}
  name: tap-injector
  labels:
    linkerd.io/extension: viz
    component: tap-injector
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: Server
    name: tap-injector-webhook
  requiredAuthenticationRefs:
  - group: policy.linkerd.io
    kind: NetworkAuthentication
    name: kube-api-server
---
apiVersion: policy.linkerd.io/v1alpha1
kind: NetworkAuthentication
metadata:
  namespace: {{ .Release.Namespace }}
  name: kube-api-server
  labels:
    linkerd.io/extension: viz
    {{- with .Values.commonLabels }}{{ toYaml . | trim | nindent 4 }}{{- end }}
  annotations:
    {{ include "partials.annotations.created-by" . }}
spec:
  # Ideally, this should be restricted to the actual set of IPs the kubelet API
  # server uses for webhooks in a cluster. This can't easily be discovered.
  networks:
  - cidr: "0.0.0.0/0"
  - cidr: "::/0"

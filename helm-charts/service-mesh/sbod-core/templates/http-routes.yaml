apiVersion: policy.linkerd.io/v1beta1
kind: HTTPRoute
metadata:
  name: sbod-core-post-route
  namespace: {{ .Values.meshedService.namespace }}
spec:
  parentRefs:
    - name: sbod-core-http
      kind: Server
      group: policy.linkerd.io
  rules:
    - matches:
        - path:
            value: "/core/"
            type: "PathPrefix"
          method: POST
---
apiVersion: policy.linkerd.io/v1beta1
kind: HTTPRoute
metadata:
  name: sbod-core-tgw-route
  namespace: {{ .Values.meshedService.namespace }}
spec:
  parentRefs:
    - name: sbod-core-http
      kind: Server
      group: policy.linkerd.io
  rules:
    - matches:
        - path:
            value: "/core/"
            type: "PathPrefix"
          method: GET
---
apiVersion: policy.linkerd.io/v1beta1
kind: HTTPRoute
metadata:
  name: sbod-core-probe-route
  namespace: {{ .Values.meshedService.namespace }}
spec:
  parentRefs:
    - name: sbod-core-http
      kind: Server
      group: policy.linkerd.io
  rules:
    - matches:
        - path:
            value: "/core/actuator/health"
          method: GET
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  name: sbod-core-post-policy
  namespace: {{ .Values.meshedService.namespace }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: HTTPRoute
    name: sbod-core-post-route
  requiredAuthenticationRefs:
    - name: sbod-ext-post-authn
      kind: MeshTLSAuthentication
      group: policy.linkerd.io
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  name: sbod-core-tgw-policy
  namespace: {{ .Values.meshedService.namespace }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: HTTPRoute
    name: sbod-core-tgw-route
  requiredAuthenticationRefs:
    - name: tgw-sbod-core-get-authn
      kind: MeshTLSAuthentication
      group: policy.linkerd.io
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  name: sbod-core-probe-policy
  namespace: {{ .Values.meshedService.namespace }}
spec:
  targetRef:
    group: policy.linkerd.io
    kind: HTTPRoute
    name: sbod-core-probe-route
  requiredAuthenticationRefs:
    - name: sbod-core-probe-authn
      kind: NetworkAuthentication
      group: policy.linkerd.io

{{ template "chart.header" . }}
{{ template "chart.description" . }}

{{ template "chart.versionBadge" . }}
{{ template "chart.typeBadge" . }}
{{ template "chart.appVersionBadge" . }}

{{ template "chart.homepageLine" . }}

## Quickstart and documentation

You can run Linkerd on any Kubernetes cluster in a matter of seconds. See the
[Linkerd Getting Started Guide][getting-started] for how.

For more comprehensive documentation, start with the [Linkerd
docs][linkerd-docs].

## Adding Linkerd's Helm repository

```bash
# To add the repo for Linkerd edge releases:
helm repo add linkerd https://helm.linkerd.io/edge
```

## Installing the linkerd-crds chart

This installs the `linkerd-crds` chart, which only persists the CRDs that
<PERSON><PERSON> requires.

After installing this chart, you need then to install the
`linkerd-control-plane` chart in the same namespace, which provides all the
linkerd core control components.

```bash
helm install linkerd-crds -n linkerd --create-namespace linkerd/linkerd-crds
```

## Get involved

* Check out <PERSON><PERSON>'s source code at [GitHub][linkerd2].
* Join <PERSON><PERSON>'s [user mailing list][linkerd-users], [developer mailing
  list][linkerd-dev], and [announcements mailing list][linkerd-announce].
* Follow [@linkerd][twitter] on Twitter.
* Join the [Linkerd Slack][slack].

[getting-started]: https://linkerd.io/2/getting-started/
[linkerd2]: https://github.com/linkerd/linkerd2
[linkerd-announce]: https://lists.cncf.io/g/cncf-linkerd-announce
[linkerd-dev]: https://lists.cncf.io/g/cncf-linkerd-dev
[linkerd-docs]: https://linkerd.io/2/overview/
[linkerd-users]: https://lists.cncf.io/g/cncf-linkerd-users
[slack]: http://slack.linkerd.io
[twitter]: https://twitter.com/linkerd

{{ template "chart.requirementsSection" . }}

{{ template "chart.valuesSection" . }}

{{ template "helm-docs.versionFooter" . }}

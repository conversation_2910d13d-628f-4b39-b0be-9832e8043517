---
###
### Service Profile CRD
###
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: serviceprofiles.linkerd.io
  annotations:
    {{ include "partials.annotations.created-by" . }}
  labels:
    helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
spec:
  group: linkerd.io
  versions:
  - name: v1alpha1
    served: true
    storage: false
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            description: Spec is the custom resource spec
            required:
            - routes
            properties:
              dstOverrides:
                type: array
                required:
                - authority
                - weight
                items:
                  type: object
                  description: WeightedDst is a weighted alternate destination.
                  properties:
                    authority:
                      type: string
                    weight:
                      x-kubernetes-int-or-string: true
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
              opaquePorts:
                type: array
                items:
                  type: string
              retryBudget:
                type: object
                required:
                - minRetriesPerSecond
                - retryRatio
                - ttl
                description: RetryBudget describes the maximum number of retries that should be issued to this service.
                properties:
                  minRetriesPerSecond:
                    format: int32
                    type: integer
                  retryRatio:
                    type: number
                    format: float
                  ttl:
                    type: string
              routes:
                type: array
                items:
                  type: object
                  description: RouteSpec specifies a Route resource.
                  required:
                  - condition
                  - name
                  properties:
                    condition:
                      type: object
                      description: RequestMatch describes the conditions under which to match a Route.
                      properties:
                        pathRegex:
                          type: string
                        method:
                          type: string
                        all:
                          type: array
                          items:
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                        any:
                          type: array
                          items:
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                        not:
                          type: array
                          items:
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                    isRetryable:
                      type: boolean
                    name:
                      type: string
                    timeout:
                      type: string
                    responseClasses:
                      type: array
                      items:
                        type: object
                        required:
                        - condition
                        description: ResponseClass describes how to classify a response (e.g. success or failures).
                        properties:
                          condition:
                            type: object
                            description: ResponseMatch describes the conditions under
                              which to classify a response.
                            properties:
                              all:
                                type: array
                                items:
                                  type: object
                                  x-kubernetes-preserve-unknown-fields: true
                              any:
                                type: array
                                items:
                                  type: object
                                  x-kubernetes-preserve-unknown-fields: true
                              not:
                                type: object
                                x-kubernetes-preserve-unknown-fields: true
                              status:
                                type: object
                                description: Range describes a range of integers (e.g. status codes).
                                properties:
                                  max:
                                    format: int32
                                    type: integer
                                  min:
                                    format: int32
                                    type: integer
                          isFailure:
                            type: boolean
  - name: v1alpha2
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            description: Spec is the custom resource spec
            properties:
              dstOverrides:
                type: array
                required:
                - authority
                - weight
                items:
                  type: object
                  description: WeightedDst is a weighted alternate destination.
                  properties:
                    authority:
                      type: string
                    weight:
                      x-kubernetes-int-or-string: true
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
              opaquePorts:
                type: array
                items:
                  type: string
              retryBudget:
                type: object
                required:
                - minRetriesPerSecond
                - retryRatio
                - ttl
                description: RetryBudget describes the maximum number of retries that should be issued to this service.
                properties:
                  minRetriesPerSecond:
                    format: int32
                    type: integer
                  retryRatio:
                    type: number
                    format: float
                  ttl:
                    type: string
              routes:
                type: array
                items:
                  type: object
                  description: RouteSpec specifies a Route resource.
                  required:
                  - condition
                  - name
                  properties:
                    condition:
                      type: object
                      description: RequestMatch describes the conditions under which to match a Route.
                      properties:
                        pathRegex:
                          type: string
                        method:
                          type: string
                        all:
                          type: array
                          items:
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                        any:
                          type: array
                          items:
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                        not:
                          type: array
                          items:
                            type: object
                            x-kubernetes-preserve-unknown-fields: true
                    isRetryable:
                      type: boolean
                    name:
                      type: string
                    timeout:
                      type: string
                    responseClasses:
                      type: array
                      items:
                        type: object
                        required:
                        - condition
                        description: ResponseClass describes how to classify a response (e.g. success or failures).
                        properties:
                          condition:
                            type: object
                            description: ResponseMatch describes the conditions under
                              which to classify a response.
                            properties:
                              all:
                                type: array
                                items:
                                  type: object
                                  x-kubernetes-preserve-unknown-fields: true
                              any:
                                type: array
                                items:
                                  type: object
                                  x-kubernetes-preserve-unknown-fields: true
                              not:
                                type: object
                                x-kubernetes-preserve-unknown-fields: true
                              status:
                                type: object
                                description: Range describes a range of integers (e.g. status codes).
                                properties:
                                  max:
                                    format: int32
                                    type: integer
                                  min:
                                    format: int32
                                    type: integer
                          isFailure:
                            type: boolean
  scope: Namespaced
  preserveUnknownFields: false
  names:
    plural: serviceprofiles
    singular: serviceprofile
    kind: ServiceProfile
    shortNames:
    - sp

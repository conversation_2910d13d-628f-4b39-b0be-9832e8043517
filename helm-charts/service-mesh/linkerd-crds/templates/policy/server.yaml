---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: servers.policy.linkerd.io
  annotations:
    {{ include "partials.annotations.created-by" . }}
  labels:
    helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
spec:
  group: policy.linkerd.io
  names:
    kind: Server
    plural: servers
    singular: server
    shortNames: [srv]
  scope: Namespaced
  versions:
    - name: v1alpha1
      served: true
      storage: false
      deprecated: true
      deprecationWarning: "policy.linkerd.io/v1alpha1 Server is deprecated; use policy.linkerd.io/v1beta1 Server"
      schema:
        openAPIV3Schema:
          type: object
          required: [spec]
          properties:
            spec:
              type: object
              required:
                - podSelector
                - port
              properties:
                podSelector:
                  type: object
                  description: >-
                    Selects pods in the same namespace.
                  oneOf:
                    - required: [matchExpressions]
                    - required: [matchLabels]
                  properties:
                    matchLabels:
                      type: object
                      x-kubernetes-preserve-unknown-fields: true
                    matchExpressions:
                      type: array
                      items:
                        type: object
                        required: [key, operator]
                        properties:
                          key:
                            type: string
                          operator:
                            type: string
                            enum: [In, NotIn, Exists, DoesNotExist]
                          values:
                            type: array
                            items:
                              type: string
                port:
                  description: >-
                    A port name or number. Must exist in a pod spec.
                  x-kubernetes-int-or-string: true
                proxyProtocol:
                  description: >-
                    Configures protocol discovery for inbound connections.

                    Supersedes the `config.linkerd.io/opaque-ports` annotation.
                  type: string
                  default: unknown
    - name: v1beta1
      served: true
      storage: false
      deprecated: true
      deprecationWarning: "policy.linkerd.io/v1alpha1 Server is deprecated; use policy.linkerd.io/v1beta2 Server"
      schema:
        openAPIV3Schema:
          type: object
          required: [spec]
          properties:
            spec:
              type: object
              required:
                - podSelector
                - port
              properties:
                podSelector:
                  type: object
                  description: >-
                    Selects pods in the same namespace.

                    The result of matchLabels and matchExpressions are ANDed.
                    Selects all if empty.
                  properties:
                    matchLabels:
                      type: object
                      x-kubernetes-preserve-unknown-fields: true
                    matchExpressions:
                      type: array
                      items:
                        type: object
                        required: [key, operator]
                        properties:
                          key:
                            type: string
                          operator:
                            type: string
                            enum: [In, NotIn, Exists, DoesNotExist]
                          values:
                            type: array
                            items:
                              type: string
                port:
                  description: >-
                    A port name or number. Must exist in a pod spec.
                  x-kubernetes-int-or-string: true
                proxyProtocol:
                  description: >-
                    Configures protocol discovery for inbound connections.

                    Supersedes the `config.linkerd.io/opaque-ports` annotation.
                  type: string
                  default: unknown
      additionalPrinterColumns:
      - name: Port
        type: string
        description: The port the server is listening on
        jsonPath: .spec.port
      - name: Protocol
        type: string
        description: The protocol of the server
        jsonPath: .spec.proxyProtocol
    - name: v1beta2
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          required: [spec]
          properties:
            spec:
              type: object
              required:
                - port
              oneOf:
                - required: [podSelector]
                - required: [externalWorkloadSelector]
              properties:
                podSelector:
                  type: object
                  description: >-
                    Selects pods in the same namespace.

                    The result of matchLabels and matchExpressions are ANDed.
                    Selects all if empty.
                  properties:
                    matchLabels:
                      type: object
                      x-kubernetes-preserve-unknown-fields: true
                    matchExpressions:
                      type: array
                      items:
                        type: object
                        required: [key, operator]
                        properties:
                          key:
                            type: string
                          operator:
                            type: string
                            enum: [In, NotIn, Exists, DoesNotExist]
                          values:
                            type: array
                            items:
                              type: string
                externalWorkloadSelector:
                  type: object
                  description: >-
                    Selects ExternalWorkloads in the same namespace.

                    The result of matchLabels and matchExpressions are ANDed.
                    Selects all if empty.
                  properties:
                    matchLabels:
                      type: object
                      x-kubernetes-preserve-unknown-fields: true
                    matchExpressions:
                      type: array
                      items:
                        type: object
                        required: [key, operator]
                        properties:
                          key:
                            type: string
                          operator:
                            type: string
                            enum: [In, NotIn, Exists, DoesNotExist]
                          values:
                            type: array
                            items:
                              type: string
                port:
                  description: >-
                    A port name or number. Must exist in a pod spec.
                  x-kubernetes-int-or-string: true
                proxyProtocol:
                  description: >-
                    Configures protocol discovery for inbound connections.

                    Supersedes the `config.linkerd.io/opaque-ports` annotation.
                  type: string
                  default: unknown
      additionalPrinterColumns:
      - name: Port
        type: string
        description: The port the server is listening on
        jsonPath: .spec.port
      - name: Protocol
        type: string
        description: The protocol of the server
        jsonPath: .spec.proxyProtocol

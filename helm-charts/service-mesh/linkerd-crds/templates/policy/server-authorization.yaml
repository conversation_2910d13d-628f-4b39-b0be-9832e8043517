---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: serverauthorizations.policy.linkerd.io
  annotations:
    {{ include "partials.annotations.created-by" . }}
  labels:
    helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    linkerd.io/control-plane-ns: {{.Release.Namespace}}
spec:
  group: policy.linkerd.io
  scope: Namespaced
  names:
    kind: ServerAuthorization
    plural: serverauthorizations
    singular: serverauthorization
    shortNames: [saz, serverauthz, srvauthz]
  versions:
    - name: v1alpha1
      served: true
      storage: false
      deprecated: true
      deprecationWarning: "policy.linkerd.io/v1alpha1 ServerAuthorization is deprecated; use policy.linkerd.io/v1beta1 ServerAuthorization"
      schema:
        openAPIV3Schema:
          type: object
          required: [spec]
          properties:
            spec:
              description: >-
                Authorizes clients to communicate with Linkerd-proxied servers.
              type: object
              required: [server, client]
              properties:
                server:
                  description: >-
                    Identifies servers in the same namespace for which this
                    authorization applies.

                    Only one of `name` or `selector` may be specified.
                  type: object
                  oneOf:
                    - required: [name]
                    - required: [selector]
                  properties:
                    name:
                      description: References a `Server` instance by name
                      type: string
                      pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
                    selector:
                      description: >-
                        A label query over servers on which this authorization applies.
                      type: object
                      properties:
                        matchLabels:
                          type: object
                          x-kubernetes-preserve-unknown-fields: true
                        matchExpressions:
                          type: array
                          items:
                            type: object
                            required: [key, operator]
                            properties:
                              key:
                                type: string
                              operator:
                                type: string
                                enum: [In, NotIn, Exists, DoesNotExist]
                              values:
                                type: array
                                items:
                                  type: string
                client:
                  description:  Describes clients authorized to access a server.
                  type: object
                  properties:
                    networks:
                      description: >-
                        Limits the client IP addresses to which this
                        authorization applies. If unset, the server chooses a
                        default (typically, all IPs or the cluster's pod
                        network).
                      type: array
                      items:
                        type: object
                        required: [cidr]
                        properties:
                          cidr:
                            type: string
                          except:
                            type: array
                            items:
                              type: string
                    unauthenticated:
                      description: >-
                        Authorizes unauthenticated clients to access a server.
                      type: boolean
                    meshTLS:
                      type: object
                      properties:
                        unauthenticatedTLS:
                          type: boolean
                          description: >-
                            Indicates that no client identity is required for
                            communication.

                            This is mostly important for the identity
                            controller, which must terminate TLS connections
                            from clients that do not yet have a certificate.
                        identities:
                          description: >-
                            Authorizes clients with the provided proxy identity
                            strings (as provided via MTLS)

                            The `*` prefix can be used to match all identities in
                            a domain. An identity string of `*` indicates that
                            all authentication clients are authorized.
                          type: array
                          items:
                            type: string
                            pattern: '^(\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$'
                        serviceAccounts:
                          description: >-
                            Authorizes clients with the provided proxy identity
                            service accounts (as provided via MTLS)
                          type: array
                          items:
                            type: object
                            required: [name]
                            properties:
                              name:
                                description: The ServiceAccount's name.
                                type: string
                                pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
                              namespace:
                                description: >-
                                  The ServiceAccount's namespace. If unset, the
                                  authorization's namespace is used.
                                type: string
                                pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
    - name: v1beta1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          required: [spec]
          properties:
            spec:
              description: >-
                Authorizes clients to communicate with Linkerd-proxied servers.
              type: object
              required: [server, client]
              properties:
                server:
                  description: >-
                    Identifies servers in the same namespace for which this
                    authorization applies.

                    Only one of `name` or `selector` may be specified.
                  type: object
                  oneOf:
                    - required: [name]
                    - required: [selector]
                  properties:
                    name:
                      description: References a `Server` instance by name
                      type: string
                      pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
                    selector:
                      description: >-
                        A label query over servers on which this authorization applies.
                      type: object
                      properties:
                        matchLabels:
                          type: object
                          x-kubernetes-preserve-unknown-fields: true
                        matchExpressions:
                          type: array
                          items:
                            type: object
                            required: [key, operator]
                            properties:
                              key:
                                type: string
                              operator:
                                type: string
                                enum: [In, NotIn, Exists, DoesNotExist]
                              values:
                                type: array
                                items:
                                  type: string
                client:
                  description:  Describes clients authorized to access a server.
                  type: object
                  properties:
                    networks:
                      description: >-
                        Limits the client IP addresses to which this
                        authorization applies. If unset, the server chooses a
                        default (typically, all IPs or the cluster's pod
                        network).
                      type: array
                      items:
                        type: object
                        required: [cidr]
                        properties:
                          cidr:
                            type: string
                          except:
                            type: array
                            items:
                              type: string
                    unauthenticated:
                      description: >-
                        Authorizes unauthenticated clients to access a server.
                      type: boolean
                    meshTLS:
                      type: object
                      properties:
                        unauthenticatedTLS:
                          type: boolean
                          description: >-
                            Indicates that no client identity is required for
                            communication.

                            This is mostly important for the identity
                            controller, which must terminate TLS connections
                            from clients that do not yet have a certificate.
                        identities:
                          description: >-
                            Authorizes clients with the provided proxy identity
                            strings (as provided via MTLS)

                            The `*` prefix can be used to match all identities in
                            a domain. An identity string of `*` indicates that
                            all authentication clients are authorized.
                          type: array
                          items:
                            type: string
                            pattern: '^(\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$'
                        serviceAccounts:
                          description: >-
                            Authorizes clients with the provided proxy identity
                            service accounts (as provided via MTLS)
                          type: array
                          items:
                            type: object
                            required: [name]
                            properties:
                              name:
                                description: The ServiceAccount's name.
                                type: string
                                pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
                              namespace:
                                description: >-
                                  The ServiceAccount's namespace. If unset, the
                                  authorization's namespace is used.
                                type: string
                                pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
      additionalPrinterColumns:
      - name: Server
        type: string
        description: The server that this grants access to
        jsonPath: .spec.server.name

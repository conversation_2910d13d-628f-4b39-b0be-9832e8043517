environment: ""
branchName: ""

global:
  fsGroup: 9002
  runAs:
    user: ""
    group: ""

sbod_core:

  runAs:
    user: 9002
    group: 9002

replicaCount: 1

image:
  repository: sbodacr.azurecr.io/sbod-core
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: workload-identity-sa

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000
autoscaling:
  enabled: false

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8050

ingress:
  enabled: false
  className: ""
  annotations:
    {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

env: []

probes:
  livenessProbe:
    httpGet:
      path: /core/actuator/health
      port: http
    initialDelaySeconds: 60
  readinessProbe:
    httpGet:
      path: /core/actuator/health
      port: http
    initialDelaySeconds: 60

resources:
  {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi


nodeSelector: {}

tolerations: []

affinity: {}

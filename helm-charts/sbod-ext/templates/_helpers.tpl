{{/*
Expand the name of the chart.
*/}}
{{- define "sbod-ext.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "sbod-ext.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "sbod-ext.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "sbod-ext.labels" -}}
helm.sh/chart: {{ include "sbod-ext.chart" . }}
{{ include "sbod-ext.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "sbod-ext.selectorLabels" -}}
app: {{ include "sbod-ext.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "sbod-ext.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "sbod-ext.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Renders the podSecurityContext.
Use: {{- include "podSecurityContext" . | nindent 6 }}
*/}}
{{- define "podSecurityContext" -}}
securityContext:
    allowPrivilegeEscalation: false
    fsGroup: {{ .Values.global.fsGroup | default 1 }}
    {{- if .Values.global.runAs -}}
    {{- if .Values.global.runAs.group }}
    runAsGroup: {{ .Values.global.runAs.group }}
    {{- end }}
    {{- if .Values.global.runAs.user }}
    runAsUser: {{ .Values.global.runAs.user }}
    {{- end }}
    {{- end }}
{{- end -}}

{{/*
Renders the containerSecurityContext.
Use: {{- include "podSecurityContext" .Values.containerBaseKey | nindent 10 }}
*/}}
{{- define "containerSecurityContext" -}}
securityContext:
    allowPrivilegeEscalation: false
    {{- if .runAs -}}
    {{- if .runAs.group }}
    runAsGroup: {{ .runAs.group }}
    {{- end }}
    {{- if .runAs.user }}
    runAsUser: {{ .runAs.user }}
    {{- end }}
    {{- else }} {}
    {{- end }}
{{- end -}}


{{/*
Renders the replicas property of a deployment setting the number of replicas depending on the
 current deploy stage and branch name
Use: {{ include "deployment.replicas" . }}
*/}}
{{- define "deployment.replicas" -}}
  {{- if (ne (toString .Values.branchName) "main") -}}
    replicas: {{ print 0 -}}
  {{- else -}}
    replicas: {{ print 1 -}}
  {{- end -}}
{{- end -}}
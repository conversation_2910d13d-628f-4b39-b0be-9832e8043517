terraform {
  required_version = ">= 0.15"

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.30.0"
    }
  }

  backend "azurerm" {
    environment      = "public"
    use_azuread_auth = true
  }
}

provider "azurerm" {
  features {}
  environment                     = "public"
  resource_provider_registrations = "none" # This is only required when the User, Service Principal, or Identity running Terraform lacks the permissions to register Azure Resource Providers.
}

module "database" {
  source                          = "./modules/db"
  location                        = var.location
  prefix                          = var.rg_name_prefix
  env_name                        = var.env_name
  region_name                     = var.region_name
  app_name                        = var.app_name
  backup_retention_days           = 7
  keyvault_id                     = var.keyvault_id
  pg_admin_login                  = "sbodadmin"
  pg_version                      = var.pg_version
  pg_storage                      = "5120"
  pg_sku_name                     = var.pg_sku_name
  pg_geo_redundant_backup_enabled = var.pg_geo_redundant_backup_enabled
  allowed_cidrs                   = var.allowed_ip_ranges
  service_principal_object_id     = var.service_principal_object_id
}

# Note: Terraform will read environment variables in the form of TF_VAR_name to find the value for a variable.
variable "location" {
  type = string
}

variable "env_name" {
  type = string
}

variable "region_name" {
  type        = string
  description = "Set one of the region short names. Examples: weu = West Europe, cus = Central USA, cno3 = China North 3"
}

variable "rg_name_prefix" {
  type = string
}

variable "app_name" {
  type = string
}

variable "allowed_ip_ranges" {
  type        = map(string)
  description = "Map of authorized cidrs"
  # https://git.i.mercedes-benz.com/gcs/CloudProxy/blob/master/doc/public_IP-Ranges.md
  default = {
    "dai-proxy"  = "***********/16"
    "netskope-1" = "**********/24"
    "netskope-2" = "**********/24"
    "netskope-3" = "************/24"
    "netskope-4" = "***********/24"
    "netskope-5" = "************/24"
    "netskope-6" = "*************/17"
  }
}

variable "tags" {
  type = map(any)
  default = {
    mic_department = ""
    mic_owner      = ""
    mic_project    = ""
    mic_service    = ""
    mic_shared     = ""
    mic_stage      = ""
  }
}

# PG Database
variable "pg_sku_name" {
  type        = string
  description = "PostgreSQL SKU Name"
  default     = "B_Gen5_1"
}

variable "pg_version" {
  type = string
}

variable "pg_geo_redundant_backup_enabled" {
  description = "Turn Geo-redundant server backups on/off. Not available for the Basic tier."
  type        = bool
  default     = false
}

variable "keyvault_id" {
  type        = string
  description = "The keyvault for storing the DB credentials"
}

variable "service_principal_object_id" {
  type        = string
  description = "Object ID of the service principal to be granted admin of PostgreSQL server"
}

data "azurerm_client_config" "current" {}

locals {
  tags = {
    mic_department = "ITH"
    mic_owner      = "PANGCHE"
    mic_project    = "ServerBasedOwnerDevice"
    mic_service    = "SBOD"
    mic_shared     = "false"
    mic_stage      = var.env_name
  }
}

resource "azurerm_resource_group" "db" {
  name     = "${var.prefix}-${var.region_name}-${var.env_name}"
  location = var.location
  tags     = local.tags
}

resource "azurerm_postgresql_flexible_server" "db" {
  name                = "${var.app_name}-pqsl-${var.region_name}-${var.env_name}"
  resource_group_name = azurerm_resource_group.db.name
  location            = azurerm_resource_group.db.location

  version    = var.pg_version
  storage_mb = 32768
  sku_name   = var.pg_sku_name
  zone       = "1"

  backup_retention_days        = var.backup_retention_days
  geo_redundant_backup_enabled = var.pg_geo_redundant_backup_enabled

  tags = local.tags

  authentication {
    active_directory_auth_enabled = true
    password_auth_enabled         = false
    tenant_id                     = data.azurerm_client_config.current.tenant_id
  }

  #  auto_grow_enabled            = true
  #  ssl_enforcement_enabled = true
  #  ssl_minimal_tls_version_enforced = "TLS1_2"
}

resource "azurerm_postgresql_flexible_server_active_directory_administrator" "sbod_owners" {
  server_name         = azurerm_postgresql_flexible_server.db.name
  resource_group_name = azurerm_resource_group.db.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  object_id           = "9cb16d72-37a8-42df-94c2-4c85730212ae"
  principal_name      = "SBOD-OWNERS"
  principal_type      = "Group"
}

resource "azurerm_postgresql_flexible_server_active_directory_administrator" "sbodadmin" {
  server_name         = azurerm_postgresql_flexible_server.db.name
  resource_group_name = azurerm_resource_group.db.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  object_id           = var.service_principal_object_id
  principal_name      = "sbodadmin"
  principal_type      = "ServicePrincipal"
}

# FIREWALL
# https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/postgresql_firewall_rule

# Allow access to Azure services
resource "azurerm_postgresql_flexible_server_firewall_rule" "postgresql" {
  server_id        = azurerm_postgresql_flexible_server.db.id
  name             = "Allow-access-to-Azure-services"
  start_ip_address = "0.0.0.0"
  end_ip_address   = "0.0.0.0"
}

# Allow defined adresses (dynamic rule)
resource "azurerm_postgresql_flexible_server_firewall_rule" "firewall_rules" {
  server_id        = azurerm_postgresql_flexible_server.db.id
  for_each         = var.allowed_cidrs
  name             = each.key
  start_ip_address = cidrhost(each.value, 0)
  end_ip_address   = cidrhost(each.value, -1)
}

# DATABASE

# collation: https://www.postgresql.org/docs/current/collation.html
# charset:   https://www.postgresql.org/docs/current/static/multibyte.html
resource "azurerm_postgresql_flexible_server_database" "db" {
  server_id = azurerm_postgresql_flexible_server.db.id
  name      = "${var.app_name}-db"
  collation = "en_US.utf8"
  charset   = "utf8"
}

# SAVE VALUES TO KEYVAULT
resource "azurerm_key_vault_secret" "pguser" {
  name         = "jdbc-admin-user"
  value        = azurerm_postgresql_flexible_server_active_directory_administrator.sbodadmin.principal_name
  key_vault_id = var.keyvault_id
  tags         = { created_by = "terraform" }
}

resource "azurerm_key_vault_secret" "pgfqdn" {
  name         = "jdbc-fqdn"
  value        = azurerm_postgresql_flexible_server.db.fqdn
  key_vault_id = var.keyvault_id
  tags         = { created_by = "terraform" }
}

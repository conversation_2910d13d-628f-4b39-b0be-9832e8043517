variable "location" {
  description = "Location in Azure (eg westeurope)."
  type        = string
}

variable "env_name" {
  type = string
}

variable "region_name" {
  type        = string
  description = "Set one of the region short names. Examples: weu = West Europe, cus = Central USA, cno3 = China North 3"
}

variable "app_name" {
  type        = string
  description = "application shortname (max 12 chars, only [a-z0-9] )"
}

variable "prefix" {
  description = "Project prefix. Usually : ece-devweub-<project>"
  type        = string
}

variable "keyvault_id" {
  type        = string
  description = "The keyvault for storing the DB credentials"
}

variable "backup_retention_days" {
  description = "Backup retention days for the server, supported values are between 7 and 35 days."
  type        = number
  default     = 7
}

variable "allowed_cidrs" {
  type        = map(string)
  description = "Map of authorized cidrs"
}

# Postgresql specific
variable "pg_admin_login" {
  type        = string
  description = "Login to authenticate to PostgreSQL Server"
}

variable "pg_version" {
  type        = string
  description = "Valid values are 9.5, 9.6, 10, 10.0, and 11"
  default     = "11"
}

variable "pg_sku_name" {
  type        = string
  description = "PostgreSQL SKU Name"
  default     = "Standard_B2ms"
}

variable "pg_geo_redundant_backup_enabled" {
  description = "Turn Geo-redundant server backups on/off. Not available for the Basic tier."
  type        = bool
  default     = false
}

variable "pg_storage" {
  type        = string
  description = "PostgreSQL Storage in MB"
  default     = "5120"
}

variable "service_principal_object_id" {
  type        = string
  description = "Object ID of the service principal to be granted admin of PostgreSQL server"
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.21.0"
    }
  }

  backend "azurerm" {
    environment      = "public"
    use_azuread_auth = true
  }

}

provider "azurerm" {
  environment                     = "public"
  resource_provider_registrations = "none" # This is only required when the User, Service Principal, or Identity running Terraform lacks the permissions to register Azure Resource Providers.
  features {}
}

locals {
  tags = {
    mic_department = "ITH"
    mic_owner      = "PANGCHE"
    mic_project    = "ServerBasedOwnerDevice"
    mic_service    = "SBOD"
    mic_shared     = "false"
    mic_stage      = var.tag_stage
  }
}

// provision publicips, resource group to be created separately
module "aks-cluster-rg-pubip" {
  source         = "./modules/aks-cluster-rg-pubip"
  shortname      = var.shortname
  rg_net_name    = var.rg_net_name
  location       = var.location
  cl_name        = var.cl_name
  tags           = local.tags
  ip_domain_name = var.ip_domain_name
}

// provision cluster and its resource group
module "aks-cluster-rg-k8s" {
  source              = "./modules/aks-cluster-rg-k8s"
  client_id           = var.client_id
  client_secret       = var.client_secret
  tags                = local.tags
  tag_stage           = var.tag_stage
  tag_cluster_version = var.tag_cluster_version
  location            = var.location
  shortname           = var.shortname
  rg_name             = var.rg_name
  cl_name             = var.cl_name
  dns_prefix          = var.dns_prefix
  vm_size             = var.vm_size
  agent_count         = var.agent_count
  kubernetes_version  = var.kubernetes_version

  vnet_name         = var.vnet_name
  vnet_ip           = var.vnet_ip
  subnet_name       = var.subnet_name
  subnet_ip         = var.subnet_ip
  max_pods_per_node = var.max_pods_per_node

  k8sOUT_id             = module.aks-cluster-rg-pubip.aks-cluster-rg-pubip-name
  k8sOUT2_id            = module.aks-cluster-rg-pubip.aks-cluster-rg-pubip2-name
  admin_group_object_id = var.admin_group_object_id

  // log_analytics_workspace_name = var.log_analytics_workspace_name
  // log_analytics_workspace_rg = var.log_analytics_workspace_rg
}

// provision route table for china clusters
module "aks-cluster-route-table" {
  count               = var.deploy_to_azurechina ? 1 : 0
  source              = "./modules/aks-cluster-route-table"
  vm_route_table_name = var.vm_route_table_name
  vm_route_name       = var.vm_route_name
  next_hop_ip         = var.next_hop_ip
  cluster_location    = module.aks-cluster-rg-k8s.cluster_location
  cluster_name        = module.aks-cluster-rg-k8s.cluster_name
  subnet_id           = module.aks-cluster-rg-k8s.subnet_id
}

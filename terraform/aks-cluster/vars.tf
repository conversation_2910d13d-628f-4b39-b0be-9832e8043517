# © [2021] Mercedes-Benz AG. All rights reserved
# --------------------------------------------------------------------------------------------------
# AKS Cluster variables
# --------------------------------------------------------------------------------------------------
variable "client_id" {
  type = string
}
variable "client_secret" {
  type = string
}
# either 'prod', 'nonprod', or 'dev'
variable "tag_stage" {
  type = string
}
variable "tag_cluster_version" {
  type = string
}
variable deploy_to_azurechina {
  type = bool
}
variable location {
  type = string
}
variable shortname {
  type = string
}
variable rg_name {
  type = string
}
# Must be the same for all Environments
variable rg_net_name {
  type = string
}
variable cl_name {
  type = string
}
variable dns_prefix {
  type = string
}
variable vm_size {
  type = string
}
variable agent_count {
  type = number
}
variable kubernetes_version {
  type = string
}
# --------------------------------------------
# Can/Should be left as is...
# --------------------------------------------
variable vnet_name {
  type = string
}
variable vnet_ip {
  type = string
}
variable subnet_name {
  type = string
}
variable subnet_ip {
  type = string
}
variable max_pods_per_node {
  type = string
}
# --------------------------------------------------------------------------------------------------


# --------------------------------------------------------------------------------------------------
# !!! Give the name of existing Log Analytic group and Workspace.
# If group/workspace does NOT exist, new will NOT be created and script will fail.
# Should be unique for all clusters in all regions!!!
# --------------------------------------------------------------------------------------------------
variable log_analytics_workspace_name {
  description = "rg and LAW must be created before running terraform"
  type = string
}
variable log_analytics_workspace_rg {
  type = string
}
# --------------------------------------------------------------------------------------------------


# --------------------------------------------------------------------------------------------------
# IP Address variables. Domain name must be unique!!!
# Public IP deploys with name: cl_name_shortname_IP in the location: var.location
# CHANGING cl_name or shortname or location WILL cause a re-deployment of current publicIP and AKS!!!
# --------------------------------------------------------------------------------------------------
variable ip_domain_name {
  type = string
}
# --------------------------------------------------------------------------------------------------

# --------------------------------------------------------------------------------------------------
# China only variables. Route Table
# --------------------------------------------------------------------------------------------------
variable "vm_route_table_name" {
  description = "Route table name"
  default = "route2natgw1"
}
variable "vm_route_name" {
  description = "Route name"
  default = "Route-to-NAT-Gateway-VM1"
}
variable "next_hop_ip" {
  description = "Next hop IP address"
  default = "***********"
}

variable "admin_group_object_id"{
  type = string
}
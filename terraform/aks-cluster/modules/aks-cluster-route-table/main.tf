resource "azurerm_route_table" "natgw" {
  name                = var.vm_route_table_name
  location            = var.cluster_location
  resource_group_name = var.cluster_name

  route {
    name                   = var.vm_route_name
    address_prefix         = "**********/24"
    next_hop_type          = "VirtualAppliance"
    next_hop_in_ip_address = var.next_hop_ip
  }
}

resource "azurerm_subnet_route_table_association" "natgw" {
  subnet_id      = var.subnet_id
  route_table_id = azurerm_route_table.natgw.id
}
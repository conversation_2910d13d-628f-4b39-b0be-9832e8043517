# # © [2021] Mercedes-Benz AG. All rights reserved
resource "azurerm_resource_group" "pubIP" {
  name     = var.rg_net_name
  location = var.location
  tags     = var.tags
}

resource "azurerm_public_ip" "k8sIN" {
  name                    = "${var.cl_name}_${var.shortname}_IP_IN"
  location                = var.location
  resource_group_name     = azurerm_resource_group.pubIP.name
  allocation_method       = "Static"
  sku                     = "Standard"
  idle_timeout_in_minutes = 30
  domain_name_label       = "in-${var.ip_domain_name}-${var.shortname}"
  tags                    = var.tags
}

resource "azurerm_public_ip" "k8sOUT" {
  name                    = "${var.cl_name}_${var.shortname}_IP_OUT"
  location                = var.location
  resource_group_name     = azurerm_resource_group.pubIP.name
  allocation_method       = "Static"
  sku                     = "Standard"
  idle_timeout_in_minutes = 30
  domain_name_label       = "out-${var.ip_domain_name}-${var.shortname}"
  tags                    = var.tags
}

resource "azurerm_public_ip" "k8sOUT2" {
  name                    = "${var.cl_name}_${var.shortname}_IP_OUT2"
  location                = var.location
  resource_group_name     = azurerm_resource_group.pubIP.name
  allocation_method       = "Static"
  sku                     = "Standard"
  idle_timeout_in_minutes = 30
  domain_name_label       = "out2-${var.ip_domain_name}-${var.shortname}"
  tags                    = var.tags
}

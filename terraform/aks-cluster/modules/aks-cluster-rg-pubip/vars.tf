variable "shortname" {
  type = string
}
# Must be the same for all Environments
variable "rg_net_name" {
  type = string
}
variable "location" {
  type = string
}
variable "cl_name" {
  type = string
}
variable "tags" {
  description = "MIC required tags"
  type        = map(string)
}
# --------------------------------------------------------------------------------------------------
# IP Address variables. Domain name must be unique!!!
# Public IP deploys with name: cl_name_shortname_IP in the location: var.location
# CHANGING cl_name or shortname or location WILL cause a re-deployment of current publicIP and AKS!!!
# --------------------------------------------------------------------------------------------------
variable "ip_domain_name" {
  type = string
}
# --------------------------------------------------------------------------------------------------

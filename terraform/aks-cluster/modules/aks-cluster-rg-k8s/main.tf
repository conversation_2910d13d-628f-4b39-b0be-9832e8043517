data "azurerm_client_config" "current" {}

resource "azurerm_resource_group" "k8s" {
  name     = "${var.rg_name}-${var.location}"
  location = var.location
  tags     = var.tags
}

resource "azurerm_virtual_network" "k8s" {
  name                = "${var.vnet_name}-${var.location}"
  location            = azurerm_resource_group.k8s.location
  resource_group_name = azurerm_resource_group.k8s.name
  address_space       = [var.vnet_ip]
  tags                = var.tags
}

resource "azurerm_subnet" "k8s" {
  name                 = "${var.subnet_name}-${var.location}"
  resource_group_name  = azurerm_resource_group.k8s.name
  address_prefixes     = [var.subnet_ip]
  virtual_network_name = azurerm_virtual_network.k8s.name

  service_endpoints = [
    "Microsoft.Storage",
    "Microsoft.AzureCosmosDB",
  ]
}

resource "azurerm_kubernetes_cluster" "k8s" {
  name                = "${var.cl_name}_${var.shortname}"
  location            = azurerm_resource_group.k8s.location
  resource_group_name = azurerm_resource_group.k8s.name
  node_resource_group = "${azurerm_resource_group.k8s.name}-node-rg"
  dns_prefix          = "${var.dns_prefix}-${var.shortname}"
  kubernetes_version  = var.kubernetes_version

  default_node_pool {
    name                        = "agentpool"
    node_count                  = var.agent_count
    vm_size                     = var.vm_size
    os_disk_size_gb             = 55
    max_pods                    = var.max_pods_per_node
    vnet_subnet_id              = azurerm_subnet.k8s.id
    orchestrator_version        = var.kubernetes_version
    temporary_name_for_rotation = "nodetemp"
  }

  #Use Microsoft Entra ID with Azure RBAC for authentication and authorization
  identity {
    type = "SystemAssigned"
  }

  azure_active_directory_role_based_access_control {
    azure_rbac_enabled     = true
    tenant_id              = data.azurerm_client_config.current.tenant_id
    admin_group_object_ids = [var.admin_group_object_id] # Add this line
  }

  role_based_access_control_enabled = true
  oidc_issuer_enabled               = true

  network_profile {
    network_plugin    = "azure"
    load_balancer_sku = "standard"
    load_balancer_profile {
      outbound_ip_address_ids = [var.k8sOUT_id, var.k8sOUT2_id]
    }
  }

  tags = merge(
    var.tags,
    {
      datadog         = var.tag_stage
      cluster_version = var.tag_cluster_version
    }
  )
}

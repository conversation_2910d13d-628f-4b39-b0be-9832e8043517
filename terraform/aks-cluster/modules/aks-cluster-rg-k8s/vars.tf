# © [2021] Mercedes-Benz AG. All rights reserved
# --------------------------------------------------------------------------------------------------
# AKS Cluster variables
# --------------------------------------------------------------------------------------------------
variable "client_id" {
  type = string
}
variable "client_secret" {
  type = string
}
variable "tags" {
  description = "MIC required tags"
  type        = map(string)
}
# either 'prod', 'nonprod', or 'dev'
variable "tag_stage" {
  type = string
}
variable "tag_cluster_version" {
  type = string
}
variable "location" {
  type = string
}
variable "shortname" {
  type = string
}
variable "rg_name" {
  type = string
}
variable "cl_name" {
  type = string
}
variable "dns_prefix" {
  type = string
}
variable "vm_size" {
  type = string
}
variable "agent_count" {
  type = number
}
variable "kubernetes_version" {
  type = string
}
# --------------------------------------------------------------------------------------------------

# --------------------------------------------
# Can/Should be left as is...
# --------------------------------------------
variable "vnet_name" {
  type = string
}
variable "vnet_ip" {
  type = string
}
variable "subnet_name" {
  type = string
}
variable "subnet_ip" {
  type = string
}
variable "max_pods_per_node" {
  type = string
}
# --------------------------------------------

# --------------------------------------------
# public IP variables
# --------------------------------------------

variable "k8sOUT_id" {
  type = string
}

variable "k8sOUT2_id" {
  type = string
}

variable "admin_group_object_id" {
  type = string
}

# --------------------------------------------------------------------------------------------------

# --------------------------------------------------------------------------------------------------
# !!! Give the name of existing Log Analytic group and Workspace.
# If group/workspace does NOT exist, new will NOT be created and script will fail.
# Should be unique for all clusters in all regions!!!
# --------------------------------------------------------------------------------------------------
// variable log_analytics_workspace_name {
//   description = "rg and LAW must be created before running terraform"
//   type = string
// }
// variable log_analytics_workspace_rg {
//   type = string
// }
# --------------------------------------------------------------------------------------------------
